#!/bin/bash

# 本机运行，容器运行的话，直接docker-compose up -d

echo "🚀 启动服务器安装管理系统..."

# 添加用户本地 bin 目录到 PATH
export PATH="$HOME/.local/bin:$PATH"

# 检查并安装依赖
if [ -f "deploy/requirements.txt" ]; then
    echo "📦 安装依赖包..."
    pip3 install -r deploy/requirements.txt --quiet --no-warn-script-location
fi

# 创建必要目录
mkdir -p logs configs

# 启动应用
echo "🌐 启动应用程序..."
echo "访问地址: http://localhost:5001"
echo "终端界面: http://localhost:5001/terminal.html"
echo "按 Ctrl+C 停止服务器"
echo ""

python3 app.py
