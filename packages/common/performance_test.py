#!/usr/bin/env python3
"""
性能测试脚本 - 对比单线程和多线程检查的性能差异
"""

import os
import subprocess
import time
import json
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed

def check_package_status_single(package_file, package_type):
    """单线程版本的包状态检查"""
    try:
        if package_type == 'python':
            cmd = f'python3 packages/{package_file} status'
        else:
            cmd = f'bash packages/{package_file} status'

        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=15)

        if result.returncode == 0:
            output = result.stdout.strip().lower()
            if 'running' in output or 'active' in output:
                return 'running'
            elif 'installed' in output or 'stopped' in output:
                return 'installed'
            else:
                return 'installed'
        else:
            return 'failed'
    except subprocess.TimeoutExpired:
        return 'timeout'
    except Exception:
        return 'unknown'

def check_package_status_multi(package_info):
    """多线程版本的包状态检查"""
    package_file = package_info['file']
    package_type = package_info['type']
    package_name = package_info['name']
    
    try:
        if package_type == 'python':
            cmd = f'python3 packages/{package_file} status'
        else:
            cmd = f'bash packages/{package_file} status'

        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=15)

        if result.returncode == 0:
            output = result.stdout.strip().lower()
            if 'running' in output or 'active' in output:
                status = 'running'
            elif 'installed' in output or 'stopped' in output:
                status = 'installed'
            else:
                status = 'installed'
        else:
            status = 'failed'
    except subprocess.TimeoutExpired:
        status = 'timeout'
    except Exception:
        status = 'unknown'
    
    return {
        'package': package_info,
        'status': status,
        'name': package_name
    }

def get_all_packages():
    """获取所有包文件"""
    packages_dir = 'packages'
    if not os.path.exists(packages_dir):
        return []

    all_packages = []
    for root, dirs, files in os.walk(packages_dir):
        if 'common' in dirs:
            dirs.remove('common')

        for file in files:
            if file.endswith('.py') or file.endswith('.sh'):
                rel_path = os.path.relpath(os.path.join(root, file), packages_dir)

                if rel_path.startswith('common/'):
                    continue

                package_type = 'python' if file.endswith('.py') else 'shell'
                all_packages.append({
                    'file': rel_path,
                    'name': file.replace('.py', '').replace('.sh', ''),
                    'type': package_type
                })

    return all_packages

def test_single_thread(packages):
    """测试单线程性能"""
    print('🔄 开始单线程测试...', flush=True)
    start_time = time.time()
    
    installed_count = 0
    for i, pkg in enumerate(packages, 1):
        print(f'  [{i}/{len(packages)}] 检查 {pkg["name"]}...', end='', flush=True)
        status = check_package_status_single(pkg['file'], pkg['type'])
        if status in ['running', 'installed', 'stopped']:
            installed_count += 1
            print(' ✅', flush=True)
        else:
            print(' ⚪', flush=True)
    
    end_time = time.time()
    duration = end_time - start_time
    
    return {
        'duration': duration,
        'installed_count': installed_count,
        'method': 'single_thread'
    }

def test_multi_thread(packages, max_workers=8):
    """测试多线程性能"""
    print(f'🚀 开始多线程测试 (线程数: {max_workers})...', flush=True)
    start_time = time.time()
    
    installed_count = 0
    completed_count = 0
    
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        future_to_package = {executor.submit(check_package_status_multi, pkg): pkg for pkg in packages}
        
        for future in as_completed(future_to_package):
            try:
                result = future.result()
                completed_count += 1
                status = result['status']
                package_name = result['name']
                
                print(f'  [{completed_count}/{len(packages)}] {package_name}...', end='', flush=True)
                
                if status in ['running', 'installed', 'stopped']:
                    installed_count += 1
                    print(' ✅', flush=True)
                else:
                    print(' ⚪', flush=True)
            except Exception as e:
                completed_count += 1
                pkg = future_to_package[future]
                print(f'  [{completed_count}/{len(packages)}] {pkg["name"]}... ❌', flush=True)
    
    end_time = time.time()
    duration = end_time - start_time
    
    return {
        'duration': duration,
        'installed_count': installed_count,
        'method': f'multi_thread_{max_workers}'
    }

def main():
    print('🧪 软件包检查性能测试')
    print('=' * 50)
    
    # 获取所有包
    packages = get_all_packages()
    print(f'📦 找到 {len(packages)} 个软件包')
    print('-' * 30)
    
    if len(packages) == 0:
        print('❌ 没有找到软件包，退出测试')
        return
    
    # 测试单线程
    single_result = test_single_thread(packages)
    
    print('-' * 30)
    
    # 测试多线程 (4线程)
    multi_result_4 = test_multi_thread(packages, 4)
    
    print('-' * 30)
    
    # 测试多线程 (8线程)
    multi_result_8 = test_multi_thread(packages, 8)
    
    print('-' * 30)
    
    # 输出结果对比
    print('📊 性能测试结果:')
    print(f'  单线程:     {single_result["duration"]:.2f}秒 (已安装: {single_result["installed_count"]})')
    print(f'  多线程(4):  {multi_result_4["duration"]:.2f}秒 (已安装: {multi_result_4["installed_count"]})')
    print(f'  多线程(8):  {multi_result_8["duration"]:.2f}秒 (已安装: {multi_result_8["installed_count"]})')
    
    # 计算性能提升
    speedup_4 = single_result["duration"] / multi_result_4["duration"]
    speedup_8 = single_result["duration"] / multi_result_8["duration"]
    
    print(f'🚀 性能提升:')
    print(f'  4线程相比单线程: {speedup_4:.2f}x 倍速')
    print(f'  8线程相比单线程: {speedup_8:.2f}x 倍速')
    
    # 保存测试结果
    test_results = {
        'test_time': datetime.now().isoformat(),
        'total_packages': len(packages),
        'results': {
            'single_thread': single_result,
            'multi_thread_4': multi_result_4,
            'multi_thread_8': multi_result_8
        },
        'speedup': {
            '4_threads': round(speedup_4, 2),
            '8_threads': round(speedup_8, 2)
        }
    }
    
    with open('performance_test_results.json', 'w', encoding='utf-8') as f:
        json.dump(test_results, f, indent=2, ensure_ascii=False)
    
    print(f'💾 测试结果已保存到 performance_test_results.json')
    print('=' * 50)

if __name__ == '__main__':
    main()
