#!/usr/bin/env python3
"""
Gitea包管理器 - 简化版
提供统一的包管理接口，支持所有类型的包

使用示例:
    # 方式1: 使用内置默认配置（最简单）
    from gitea_package_manager import upload_file, download_file, upload_docker, test_connection

    # 直接使用便捷函数，无需配置
    upload_file("my-package", "1.0.0", "file.zip")
    download_file("my-package", "1.0.0", "file.zip", "downloaded.zip")
    upload_docker("my-app", "latest", "my-app:latest")

    # 方式2: 使用默认配置的管理器
    from gitea_package_manager import GiteaPackageManager

    manager = GiteaPackageManager()  # 自动使用内置配置
    manager.upload("generic", "my-package", "1.0.0", file_path="file.zip")
    manager.download("docker", "my-app", "latest")

    # 方式3: 自定义配置
    manager = GiteaPackageManager(
        gitea_url="https://your-gitea.com",
        username="your_username",
        token="your_token"
    )
"""

import os
import subprocess
import requests
import logging
from typing import Optional, Dict, List

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# ==================== 默认配置 ====================

# 默认Gitea配置（可以直接使用，无需外部配置文件）
DEFAULT_GITEA_CONFIG = {
    "gitea_url": "https://git.sanyitec.cc",
    "username": "sanyitec",
    "token": "4f89a50292fee68909204e86bc96997704ace259",
    "verify_ssl": False  # 内网环境推荐设置为False
}

# 用户信息
USER_INFO = {
    "username": "sanyitec",
    "email": "<EMAIL>",
    "gitea_url": "https://git.sanyitec.cc/"
}

class GiteaPackageManager:
    """Gitea包管理器 - 简化版，提供统一接口"""

    def __init__(self, gitea_url: str = None, username: str = None, token: str = None, verify_ssl: bool = None):
        """
        初始化Gitea包管理器

        Args:
            gitea_url: Gitea服务器URL，如 https://git.sanyitec.cc（可选，使用默认配置）
            username: 用户名（可选，使用默认配置）
            token: 访问令牌（可选，使用默认配置）
            verify_ssl: 是否验证SSL证书（可选，使用默认配置）

        如果不提供参数，将使用内置的默认配置
        """
        # 使用默认配置或提供的参数
        self.gitea_url = (gitea_url or DEFAULT_GITEA_CONFIG["gitea_url"]).rstrip('/')
        self.username = username or DEFAULT_GITEA_CONFIG["username"]
        self.token = token or DEFAULT_GITEA_CONFIG["token"]
        self.verify_ssl = verify_ssl if verify_ssl is not None else DEFAULT_GITEA_CONFIG["verify_ssl"]

        # API基础URL
        self.api_base = f"{self.gitea_url}/api/v1"
        self.packages_base = f"{self.gitea_url}/api/packages/{self.username}"

        # 请求头
        self.headers = {
            'Authorization': f'token {self.token}',
            'User-Agent': 'GiteaPackageManager/2.0'
        }

        # 禁用SSL警告（如果不验证SSL）
        if not verify_ssl:
            import urllib3
            urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

        # 支持的包类型 - 简化版本
        self.supported_types = [
            'generic',  # 所有二进制文件统一使用generic类型
            'docker',   # Docker镜像
            'npm',      # Node.js包
            'python',   # Python包
            'pypi'      # Python PyPI包
        ]

        # 支持的文件格式（用于文件名识别，但都作为generic处理）
        self.supported_formats = [
            # Linux包格式
            'deb', 'rpm', 'apk', 'snap', 'flatpak', 'appimage',
            # 压缩包格式
            'zip', 'tar', 'tar.gz', 'tgz', 'tar.xz', 'tar.bz2', 'rar', '7z',
            # Windows格式
            'exe', 'msi',
            # macOS格式
            'dmg', 'pkg',
            # 开发包格式
            'jar', 'war', 'gem', 'wheel', 'nupkg', 'vsix',
            # 镜像格式
            'iso', 'ova', 'qcow2'
        ]

    @classmethod
    def create_default(cls):
        """
        使用默认配置创建包管理器实例

        Returns:
            GiteaPackageManager: 使用默认配置的实例
        """
        return cls()

    @classmethod
    def create_with_config(cls, config_dict: dict):
        """
        使用配置字典创建包管理器实例

        Args:
            config_dict: 包含配置信息的字典

        Returns:
            GiteaPackageManager: 配置好的实例
        """
        return cls(
            gitea_url=config_dict.get("gitea_url"),
            username=config_dict.get("username"),
            token=config_dict.get("token"),
            verify_ssl=config_dict.get("verify_ssl")
        )

    def get_current_config(self) -> dict:
        """
        获取当前配置信息

        Returns:
            dict: 当前配置（token会被隐藏）
        """
        return {
            "gitea_url": self.gitea_url,
            "username": self.username,
            "token": "***HIDDEN***",
            "verify_ssl": self.verify_ssl
        }

    def test_connection(self) -> bool:
        """测试与Gitea的连接"""
        try:
            # 先测试基本连接（不需要认证）
            response = requests.get(
                f"{self.api_base}/version",
                verify=self.verify_ssl,
                timeout=10
            )

            if response.status_code == 200:
                version_info = response.json()
                logging.info(f"✅ Gitea服务连接成功，版本: {version_info.get('version', 'N/A')}")

                # 如果有token，测试用户认证
                if self.token and self.token != "dummy_token":
                    user_response = requests.get(
                        f"{self.api_base}/user",
                        headers=self.headers,
                        verify=self.verify_ssl,
                        timeout=10
                    )

                    if user_response.status_code == 200:
                        user_info = user_response.json()
                        logging.info(f"✅ 用户认证成功: {user_info.get('login')}")
                    elif user_response.status_code == 401:
                        logging.warning(f"⚠️ Token认证失败，但服务连接正常")
                    else:
                        logging.warning(f"⚠️ 用户API响应异常: {user_response.status_code}")
                else:
                    logging.info(f"ℹ️ 未提供有效token，仅测试服务连接")

                return True
            else:
                logging.error(f"❌ 服务连接失败: {response.status_code}")
                return False

        except Exception as e:
            logging.error(f"❌ 连接异常: {e}")
            return False

    # ==================== 统一的包管理接口 ====================

    def upload(self, package_type: str, package_name: str, version: str = None,
               file_path: str = None, filename: str = None, package_dir: str = None,
               package_file: str = None, local_image: str = None) -> bool:
        """
        统一的上传接口

        Args:
            package_type: 包类型 ('generic', 'docker', 'npm', 'python', 'deb', 'rpm', 'zip', 'tar.gz', 'exe', 'appimage'等)
            package_name: 包名
            version: 版本号
            file_path: 文件路径 (generic包使用)
            filename: 文件名 (generic包使用)
            package_dir: 包目录 (npm包使用)
            package_file: 包文件 (python包使用)
            local_image: 本地镜像标签 (docker使用)

        Returns:
            bool: 上传是否成功
        """
        try:
            # 简化的类型处理：将所有文件格式统一为generic处理
            if package_type in self.supported_formats or package_type not in self.supported_types:
                logging.info(f"📤 上传{package_type}格式文件作为generic包: {package_name}")
                return self._upload_generic(package_name, version, file_path, filename)

            logging.info(f"📤 上传{package_type}包: {package_name}")

            if package_type == 'generic':
                return self._upload_generic(package_name, version, file_path, filename)
            elif package_type == 'docker':
                return self._upload_docker(package_name, version, local_image)
            elif package_type == 'npm':
                return self._upload_npm(package_dir or package_name)
            elif package_type in ['python', 'pypi']:
                return self._upload_python(package_file or file_path)
            else:
                logging.error(f"❌ 包类型 {package_type} 暂未实现")
                return False

        except Exception as e:
            logging.error(f"❌ 上传异常: {e}")
            return False

    def download(self, package_type: str, package_name: str, version: str = None,
                filename: str = None, local_path: str = None) -> bool:
        """
        统一的下载接口

        Args:
            package_type: 包类型 ('generic', 'docker', 'npm', 'python', 'deb', 'rpm', 'zip', 'tar.gz', 'exe', 'appimage'等)
            package_name: 包名
            version: 版本号
            filename: 文件名 (generic包使用)
            local_path: 本地保存路径

        Returns:
            bool: 下载是否成功
        """
        try:
            # 简化的类型处理：将所有文件格式统一为generic处理
            if package_type in self.supported_formats or package_type not in self.supported_types:
                logging.info(f"📥 下载{package_type}格式文件作为generic包: {package_name}")
                return self._download_generic(package_name, version, filename, local_path)

            logging.info(f"📥 下载{package_type}包: {package_name}")

            if package_type == 'generic':
                return self._download_generic(package_name, version, filename, local_path)
            elif package_type == 'docker':
                return self._download_docker(package_name, version)
            elif package_type in ['python', 'pypi']:
                return self._download_python(package_name, version)
            else:
                logging.error(f"❌ 包类型 {package_type} 暂未实现下载功能")
                return False

        except Exception as e:
            logging.error(f"❌ 下载异常: {e}")
            return False

    # ==================== 内部实现方法 ====================

    def _upload_generic(self, package_name: str, version: str, file_path: str, filename: str = None) -> bool:
        """上传Generic包的内部实现"""
        try:
            if not os.path.exists(file_path):
                logging.error(f"❌ 文件不存在: {file_path}")
                return False

            if filename is None:
                filename = os.path.basename(file_path)

            url = f"{self.packages_base}/generic/{package_name}/{version}/{filename}"

            with open(file_path, 'rb') as f:
                response = requests.put(
                    url,
                    data=f,
                    headers={'Authorization': f'token {self.token}'},
                    verify=self.verify_ssl,
                    timeout=300
                )

            if response.status_code in [200, 201]:
                logging.info(f"✅ Generic包上传成功")
                return True
            else:
                logging.error(f"❌ Generic包上传失败: {response.status_code}")
                return False

        except Exception as e:
            logging.error(f"❌ Generic包上传异常: {e}")
            return False

    def _download_generic(self, package_name: str, version: str, filename: str, local_path: str) -> bool:
        """下载Generic包的内部实现"""
        try:
            url = f"{self.packages_base}/generic/{package_name}/{version}/{filename}"
            logging.info(f"开始下载: {url}")

            # 优化：减少超时时间，避免长时间等待
            response = requests.get(
                url,
                headers=self.headers,
                verify=self.verify_ssl,
                timeout=30,  # 从300秒减少到30秒
                stream=True
            )

            logging.info(f"响应状态: {response.status_code}")

            if response.status_code == 200:
                local_dir = os.path.dirname(local_path)
                if local_dir:
                    os.makedirs(local_dir, exist_ok=True)

                logging.info(f"开始写入文件: {local_path}")
                downloaded = 0
                with open(local_path, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        f.write(chunk)
                        downloaded += len(chunk)
                        # 每10MB打印一次进度
                        if downloaded % (10 * 1024 * 1024) == 0:
                            logging.info(f"已下载: {downloaded:,} 字节")

                logging.info(f"✅ Generic包下载成功: {filename}, 总大小: {downloaded:,} 字节")
                return True
            elif response.status_code == 404:
                # 404错误不需要记录为ERROR，这是正常的尝试过程
                logging.debug(f"📥 文件不存在: {filename}")
                return False
            else:
                logging.error(f"❌ Generic包下载失败: {response.status_code}")
                return False

        except requests.exceptions.Timeout:
            logging.error(f"📥 下载超时: {filename}")
            return False
        except Exception as e:
            logging.error(f"❌ Generic包下载异常: {e}")
            return False

    def _upload_docker(self, package_name: str, version: str, local_image: str = None) -> bool:
        """上传Docker镜像的内部实现"""
        try:
            # 先登录
            if not self._docker_login():
                return False

            registry_url = self.gitea_url.replace('https://', '').replace('http://', '')
            full_image_tag = f"{registry_url}/{self.username}/{package_name}:{version}"

            # 如果指定了本地镜像，先重新标记
            if local_image:
                tag_cmd = ["docker", "tag", local_image, full_image_tag]
                result = subprocess.run(tag_cmd, capture_output=True, text=True)
                if result.returncode != 0:
                    logging.error(f"❌ 镜像标记失败: {result.stderr}")
                    return False

            # 推送镜像
            push_cmd = ["docker", "push", full_image_tag]
            result = subprocess.run(push_cmd, capture_output=True, text=True)

            if result.returncode == 0:
                logging.info("✅ Docker镜像推送成功")
                return True
            else:
                logging.error(f"❌ Docker镜像推送失败: {result.stderr}")
                return False

        except Exception as e:
            logging.error(f"❌ Docker推送异常: {e}")
            return False

    def _download_docker(self, package_name: str, version: str) -> bool:
        """下载Docker镜像的内部实现"""
        try:
            # 先登录
            if not self._docker_login():
                return False

            registry_url = self.gitea_url.replace('https://', '').replace('http://', '')
            full_image_tag = f"{registry_url}/{self.username}/{package_name}:{version}"

            pull_cmd = ["docker", "pull", full_image_tag]
            result = subprocess.run(pull_cmd, capture_output=True, text=True)

            if result.returncode == 0:
                logging.info("✅ Docker镜像拉取成功")
                return True
            else:
                logging.error(f"❌ Docker镜像拉取失败: {result.stderr}")
                return False

        except Exception as e:
            logging.error(f"❌ Docker拉取异常: {e}")
            return False

    def _docker_login(self) -> bool:
        """登录到Docker Registry"""
        try:
            registry_url = self.gitea_url.replace('https://', '').replace('http://', '')

            login_cmd = [
                "docker", "login",
                "-u", self.username,
                "--password-stdin",
                registry_url
            ]

            result = subprocess.run(
                login_cmd,
                input=self.token,
                text=True,
                capture_output=True
            )

            if result.returncode == 0:
                logging.info("✅ Docker Registry登录成功")
                return True
            else:
                logging.error(f"❌ Docker Registry登录失败: {result.stderr}")
                return False

        except Exception as e:
            logging.error(f"❌ Docker登录异常: {e}")
            return False

    def _upload_npm(self, package_dir: str) -> bool:
        """上传npm包的内部实现"""
        try:
            if not os.path.exists(os.path.join(package_dir, "package.json")):
                logging.error(f"❌ 目录中没有package.json: {package_dir}")
                return False

            # 配置npm registry
            registry_url = f"{self.gitea_url}/api/packages/{self.username}/npm/"
            registry_host = self.gitea_url.replace('https://', '').replace('http://', '')

            # 设置registry和认证
            npm_config_cmds = [
                ["npm", "config", "set", "registry", registry_url],
                ["npm", "config", "set", f"//{registry_host}/api/packages/{self.username}/npm/:_authToken", self.token]
            ]

            for cmd in npm_config_cmds:
                result = subprocess.run(cmd, capture_output=True, text=True)
                if result.returncode != 0:
                    logging.error(f"❌ npm配置失败: {result.stderr}")
                    return False

            # 发布包
            publish_cmd = ["npm", "publish"]
            result = subprocess.run(
                publish_cmd,
                cwd=package_dir,
                capture_output=True,
                text=True
            )

            if result.returncode == 0:
                logging.info("✅ npm包发布成功")
                return True
            else:
                logging.error(f"❌ npm包发布失败: {result.stderr}")
                return False

        except Exception as e:
            logging.error(f"❌ npm发布异常: {e}")
            return False

    def _upload_python(self, package_file: str) -> bool:
        """上传Python包的内部实现"""
        try:
            if not os.path.exists(package_file):
                logging.error(f"❌ 包文件不存在: {package_file}")
                return False

            pypi_url = f"{self.gitea_url}/api/packages/{self.username}/pypi/"

            upload_cmd = [
                "twine", "upload",
                "--repository-url", pypi_url,
                "--username", self.username,
                "--password", self.token,
                package_file
            ]

            result = subprocess.run(upload_cmd, capture_output=True, text=True)

            if result.returncode == 0:
                logging.info("✅ Python包上传成功")
                return True
            else:
                logging.error(f"❌ Python包上传失败: {result.stderr}")
                return False

        except Exception as e:
            logging.error(f"❌ Python包上传异常: {e}")
            return False

    def _download_python(self, package_name: str, version: str = None) -> bool:
        """下载Python包的内部实现"""
        try:
            index_url = f"{self.gitea_url}/api/packages/{self.username}/pypi/simple/"
            registry_host = self.gitea_url.replace('https://', '').replace('http://', '')

            if version:
                package_spec = f"{package_name}=={version}"
            else:
                package_spec = package_name

            install_cmd = [
                "pip", "install",
                "--index-url", index_url,
                "--trusted-host", registry_host,
                package_spec
            ]

            result = subprocess.run(install_cmd, capture_output=True, text=True)

            if result.returncode == 0:
                logging.info("✅ Python包安装成功")
                return True
            else:
                logging.error(f"❌ Python包安装失败: {result.stderr}")
                return False

        except Exception as e:
            logging.error(f"❌ Python包安装异常: {e}")
            return False

    # ==================== 包信息查询和管理 ====================

    def list_packages(self, package_type: str = None) -> List[Dict]:
        """
        列出所有包

        Args:
            package_type: 包类型过滤（可选）

        Returns:
            List[Dict]: 包列表
        """
        try:
            url = f"{self.api_base}/packages/{self.username}"

            params = {}
            if package_type:
                params['type'] = package_type

            response = requests.get(
                url,
                headers=self.headers,
                params=params,
                verify=self.verify_ssl,
                timeout=30
            )

            if response.status_code == 200:
                packages = response.json()
                logging.info(f"✅ 找到 {len(packages)} 个包")
                return packages
            else:
                logging.error(f"❌ 获取包列表失败: {response.status_code}")
                return []

        except Exception as e:
            logging.error(f"❌ 列出包异常: {e}")
            return []

    def get_package_info(self, package_type: str, package_name: str) -> Optional[Dict]:
        """
        获取包详细信息

        Args:
            package_type: 包类型
            package_name: 包名

        Returns:
            Optional[Dict]: 包信息
        """
        try:
            url = f"{self.packages_base}/{package_type}/{package_name}"

            response = requests.get(
                url,
                headers=self.headers,
                verify=self.verify_ssl,
                timeout=30
            )

            if response.status_code == 200:
                package_info = response.json()
                logging.info(f"✅ 获取包信息成功: {package_name}")
                return package_info
            else:
                logging.error(f"❌ 获取包信息失败: {response.status_code}")
                return None

        except Exception as e:
            logging.error(f"❌ 获取包信息异常: {e}")
            return None

    def get_package_files(self, package_type: str, package_name: str, version: str) -> List[str]:
        """
        获取指定包版本的文件列表 - 基于标准化格式推断

        由于Gitea的generic包API不支持获取文件列表，我们基于上传时的标准化格式直接推断

        Args:
            package_type: 包类型
            package_name: 包名
            version: 版本号

        Returns:
            List[str]: 文件名列表
        """
        try:
            # 基于上传时的标准化格式直接生成文件名
            # 这样避免了网络请求，提高了效率

            if package_type == "deb":
                # 对于deb包，优先使用标准格式
                files = [f"{package_name}_{version}.deb"]
            elif package_type in ["tar.gz", "tgz"]:
                files = [f"{package_name}_{version}.tar.gz"]
            elif package_type == "appimage":
                files = [f"{package_name}_{version}.AppImage"]
            elif package_type == "zip":
                files = [f"{package_name}_{version}.zip"]
            else:
                # 通用格式，根据常见情况推断
                files = [f"{package_name}_{version}.deb"]  # 默认为deb格式

            logging.info(f"✅ 推断文件名: {files}")
            return files

        except Exception as e:
            logging.error(f"❌ 获取包文件列表异常: {e}")
            return []



    def delete_package(self, package_type: str, package_name: str, version: str = None) -> bool:
        """
        删除包或包版本

        Args:
            package_type: 包类型
            package_name: 包名
            version: 版本号（可选，如果不指定则删除整个包）

        Returns:
            bool: 删除是否成功
        """
        try:
            if version:
                url = f"{self.api_base}/packages/{self.username}/{package_type}/{package_name}/{version}"
                logging.info(f"🗑️ 删除包版本: {package_name} v{version}")
            else:
                url = f"{self.api_base}/packages/{self.username}/{package_type}/{package_name}"
                logging.info(f"🗑️ 删除整个包: {package_name}")

            response = requests.delete(
                url,
                headers=self.headers,
                verify=self.verify_ssl,
                timeout=30
            )

            if response.status_code in [200, 204]:
                logging.info("✅ 包删除成功")
                return True
            else:
                logging.error(f"❌ 包删除失败: {response.status_code}")
                return False

        except Exception as e:
            logging.error(f"❌ 包删除异常: {e}")
            return False

# ==================== 快速使用示例 ====================

def quick_demo():
    """快速演示：列出包并允许用户删除（支持分页）"""
    print("🚀 Gitea包管理器 - 包管理演示")
    print("="*50)

    # 初始化管理器
    manager = GiteaPackageManager()

    # 测试连接
    print("🔗 测试连接...")
    if not manager.test_connection():
        print("❌ 连接失败，请检查配置")
        return

    print("✅ 连接成功")

    page_size = 10  # 每页显示的包数量
    current_page = 0  # 当前页码（从0开始）

    while True:
        # 获取所有包
        print(f"\n📦 获取包列表...")
        packages = manager.list_packages()

        if not packages:
            print("📭 没有找到任何包")
            break

        print(f"✅ 找到 {len(packages)} 个包")

        # 计算分页
        total_pages = (len(packages) + page_size - 1) // page_size
        start_idx = current_page * page_size
        end_idx = min(start_idx + page_size, len(packages))
        current_packages = packages[start_idx:end_idx]

        # 显示当前页的包
        print(f"\n📋 第 {current_page + 1}/{total_pages} 页 (共 {len(packages)} 个包):")
        print("-" * 60)
        print(f"{'序号':<4} {'包名':<25} {'类型':<10} {'版本':<15}")
        print("-" * 60)

        for i, pkg in enumerate(current_packages):
            name = pkg.get('name', 'N/A')
            pkg_type = pkg.get('type', 'N/A')
            version = pkg.get('version', 'N/A')
            print(f"{i+1:<4} {name:<25} {pkg_type:<10} {version:<15}")

        print("-" * 60)

        # 等待用户选择
        print(f"\n🎯 请选择操作:")
        print(f"   1-{len(current_packages)}: 删除对应序号的包")
        if current_page < total_pages - 1:
            print(f"   N: 下一页")
        if current_page > 0:
            print(f"   F: 前一页")
        print(f"   Q 退出")

        try:
            choice = input("\n请输入选择: ").strip().lower()

            if choice == 'q':
                print("👋 退出程序")
                break
            elif choice == 'n' and current_page < total_pages - 1:
                current_page += 1
                print(f"📄 切换到第 {current_page + 1} 页")
                continue
            elif choice == 'f' and current_page > 0:
                current_page -= 1
                print(f"📄 切换到第 {current_page + 1} 页")
                continue
            elif choice.isdigit():
                choice_num = int(choice)
                if 1 <= choice_num <= len(current_packages):
                    # 删除选中的包
                    selected_pkg = current_packages[choice_num - 1]
                    delete_package_direct(manager, selected_pkg)
                    # 删除后重新获取包列表，可能需要调整页码
                    if start_idx >= len(packages) - 1 and current_page > 0:
                        current_page -= 1
                else:
                    print(f"❌ 无效选择，请输入 1-{len(current_packages)} 之间的数字")
            else:
                print("❌ 无效输入，请重新选择")

        except KeyboardInterrupt:
            print(f"\n\n👋 用户中断，退出程序")
            break
        except Exception as e:
            print(f"❌ 输入错误: {e}")

def delete_package_direct(manager, package_info):
    """直接删除包"""
    name = package_info.get('name', 'N/A')
    pkg_type = package_info.get('type', 'N/A')
    version = package_info.get('version', 'N/A')

    print(f"\n🗑️ 删除包: {name} ({pkg_type}) v{version}")

    try:
        success = manager.delete_package(pkg_type, name, version)

        if success:
            print(f"✅ 包删除成功: {name} v{version}")
        else:
            print(f"❌ 包删除失败: {name} v{version}")

    except Exception as e:
        print(f"❌ 删除过程中出现异常: {e}")


if __name__ == "__main__":
    # 可以运行主演示或快速演示
    quick_demo()
