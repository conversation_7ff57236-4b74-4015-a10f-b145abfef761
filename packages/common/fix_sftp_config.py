#!/usr/bin/env python3
"""
SFTP配置修复脚本
用途: 诊断和修复SFTP子系统配置问题
前提: 需要sudo无密码权限
"""

import os
import sys
import subprocess
import time
import glob
from datetime import datetime
from pathlib import Path


class SftpConfigFixer:
    """SFTP配置修复器"""

    def __init__(self):
        self.ssh_config_path = "/etc/ssh/sshd_config"
        self.common_sftp_paths = [
            "/usr/lib/openssh/sftp-server",
            "/usr/libexec/openssh/sftp-server",
            "/usr/lib/sftp-server",
            "/usr/libexec/sftp-server"
        ]

    def run_command(self, cmd, check=True, capture_output=True, shell=False):
        """运行系统命令"""
        try:
            if isinstance(cmd, str) and not shell:
                cmd = cmd.split()
            result = subprocess.run(cmd, capture_output=capture_output,
                                  text=True, check=check, shell=shell)
            return result
        except subprocess.CalledProcessError as e:
            if check:
                raise
            return e

    def check_sudo_permission(self):
        """检查sudo无密码权限"""
        try:
            result = self.run_command("sudo -n true", capture_output=False)
            return result.returncode == 0
        except:
            return False

    def check_sftp_subsystem(self):
        """检查SFTP子系统配置"""
        print("📋 检查SFTP子系统配置...")

        try:
            # 使用sudo读取配置文件
            result = self.run_command(f"sudo cat {self.ssh_config_path}", shell=True)
            content = result.stdout

            # 查找SFTP子系统配置
            for line in content.split('\n'):
                if line.strip().startswith('Subsystem') and 'sftp' in line and not line.strip().startswith('#'):
                    print(f"✅ 找到SFTP子系统配置: {line.strip()}")
                    return True

            print("❌ 未找到SFTP子系统配置")
            return False
        except Exception as e:
            print(f"❌ 检查配置文件失败: {e}")
            return False

    def find_sftp_server(self):
        """查找sftp-server程序"""
        print("Finding sftp-server program...", file=sys.stderr)

        # 检查常见位置
        for path in self.common_sftp_paths:
            if os.path.isfile(path):
                print(f"Found sftp-server: {path}", file=sys.stderr)
                return path

        # 使用find命令搜索
        print("Searching for sftp-server program...", file=sys.stderr)
        try:
            result = self.run_command("find /usr -name sftp-server -type f", shell=True)
            if result.stdout.strip():
                found_path = result.stdout.strip().split('\n')[0]
                print(f"Found sftp-server: {found_path}", file=sys.stderr)
                return found_path
        except:
            pass

        print("sftp-server program not found", file=sys.stderr)
        return None

    def install_openssh_server(self):
        """安装openssh-server"""
        print("📋 安装openssh-server...")

        try:
            # 更新包列表
            self.run_command("sudo apt update -qq", shell=True)

            # 安装openssh-server
            self.run_command("sudo apt install -y openssh-server", shell=True)

            print("✅ openssh-server安装完成")
            return True
        except Exception as e:
            print(f"❌ 安装openssh-server失败: {e}")
            return False

    def configure_sftp_subsystem(self, sftp_server_path):
        """配置SFTP子系统"""
        print("📋 配置SFTP子系统...")

        try:
            # 备份配置文件
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_path = f"{self.ssh_config_path}.backup.{timestamp}"
            self.run_command(f"sudo cp {self.ssh_config_path} {backup_path}", shell=True)
            print("✅ 已备份SSH配置文件")

            # 读取当前配置
            result = self.run_command(f"sudo cat {self.ssh_config_path}", shell=True)
            lines = result.stdout.splitlines(keepends=True)

            # 注释掉现有的SFTP配置
            modified_lines = []
            for line in lines:
                if line.strip().startswith('Subsystem') and 'sftp' in line:
                    print("⚠️ SFTP子系统配置已存在，更新配置...")
                    modified_lines.append(f"#{line}")
                else:
                    modified_lines.append(line)

            # 添加新的SFTP配置
            modified_lines.extend([
                "\n",
                "# SFTP subsystem configuration\n",
                f"Subsystem sftp {sftp_server_path}\n"
            ])

            # 写入临时文件到用户可写的目录
            import tempfile
            temp_config = f"/tmp/sshd_config_temp_{os.getpid()}"
            with open(temp_config, 'w') as f:
                f.writelines(modified_lines)

            # 移动到正式位置
            self.run_command(f"sudo mv {temp_config} {self.ssh_config_path}", shell=True)

            print("SFTP subsystem configuration added")
            return True
        except Exception as e:
            print(f"❌ 配置SFTP子系统失败: {e}")
            return False

    def verify_ssh_config(self):
        """验证SSH配置"""
        print("📋 验证SSH配置语法...")

        try:
            self.run_command("sudo sshd -t", shell=True)
            print("✅ SSH配置语法正确")
            return True
        except:
            print("❌ SSH配置语法错误")
            return False

    def restart_ssh_service(self):
        """重启SSH服务"""
        print("📋 重启SSH服务...")

        try:
            # 尝试重新加载配置
            try:
                self.run_command("sudo systemctl reload ssh", shell=True)
                print("✅ SSH服务配置已重新加载")
            except:
                try:
                    self.run_command("sudo systemctl reload sshd", shell=True)
                    print("✅ SSH服务配置已重新加载")
                except:
                    print("⚠️ 重新加载失败，尝试重启服务...")
                    try:
                        self.run_command("sudo systemctl restart ssh", shell=True)
                        print("✅ SSH服务已重启")
                    except:
                        self.run_command("sudo systemctl restart sshd", shell=True)
                        print("✅ SSH服务已重启")

            # 等待服务稳定
            time.sleep(3)

            # 检查服务状态
            try:
                self.run_command("systemctl is-active --quiet ssh", shell=True)
                print("✅ SSH服务运行正常")
                return True
            except:
                try:
                    self.run_command("systemctl is-active --quiet sshd", shell=True)
                    print("✅ SSH服务运行正常")
                    return True
                except:
                    print("❌ SSH服务未正常运行")
                    return False
        except Exception as e:
            print(f"❌ SSH服务重启失败: {e}")
            return False

    def test_sftp_connection(self):
        """测试SFTP连接"""
        print("📋 测试SFTP连接...")

        try:
            # 创建测试文件
            test_file = f"/tmp/sftp_test_{int(time.time())}.txt"
            with open(test_file, 'w') as f:
                f.write(f"SFTP test file created at {datetime.now()}")

            # 测试SFTP命令
            try:
                cmd = f'echo "put {test_file}" | sftp -o BatchMode=yes -o StrictHostKeyChecking=no localhost'
                self.run_command(cmd, shell=True)
                print("✅ SFTP连接测试成功")
                os.remove(test_file)
                return True
            except:
                print("❌ SFTP连接测试失败")
                if os.path.exists(test_file):
                    os.remove(test_file)
                return False
        except Exception as e:
            print(f"❌ SFTP连接测试异常: {e}")
            return False

    def check_firewall(self):
        """检查防火墙设置"""
        print("📋 检查防火墙设置...")

        # 检查ufw状态
        try:
            result = self.run_command("sudo ufw status", shell=True, check=False)
            if result.returncode == 0:
                status_line = result.stdout.split('\n')[0]
                print(f"UFW状态: {status_line}")

                if "active" in status_line.lower():
                    print("⚠️ UFW防火墙已启用，检查SSH规则...")
                    ssh_rules = [line for line in result.stdout.split('\n')
                               if '22' in line or 'ssh' in line.lower()]
                    if ssh_rules:
                        for rule in ssh_rules:
                            print(f"SSH规则: {rule}")
                    else:
                        print("⚠️ 未找到SSH相关规则")
        except:
            pass

        # 检查iptables
        try:
            result = self.run_command("sudo iptables -L", shell=True, check=False)
            if result.returncode == 0:
                ssh_rules = [line for line in result.stdout.split('\n')
                           if 'ssh' in line.lower() or '22' in line]
                if ssh_rules:
                    print(f"iptables中找到 {len(ssh_rules)} 条SSH相关规则")
        except:
            pass

    def fix_sftp(self):
        """主修复流程"""
        print("=== SFTP配置修复脚本 ===")
        print("开始诊断SFTP配置问题...")
        print()

        # 1. 检查当前SFTP配置
        if self.check_sftp_subsystem():
            print("SFTP子系统配置存在，检查是否正常工作...")
        else:
            print("SFTP子系统配置缺失，需要添加配置")
        print()

        # 2. 查找sftp-server程序
        sftp_server_path = self.find_sftp_server()
        if not sftp_server_path:
            print("sftp-server程序不存在，安装openssh-server...")
            if not self.install_openssh_server():
                print("❌ 安装openssh-server失败")
                return False
            print()

            # 重新查找
            sftp_server_path = self.find_sftp_server()
            if not sftp_server_path:
                print("❌ 安装后仍未找到sftp-server程序")
                return False
        print()

        # 3. 配置SFTP子系统
        if not self.configure_sftp_subsystem(sftp_server_path):
            return False
        print()

        # 4. 验证配置
        if not self.verify_ssh_config():
            print("❌ SSH配置验证失败，恢复备份...")
            try:
                # 查找最新的备份文件
                backup_files = glob.glob(f"{self.ssh_config_path}.backup.*")
                if backup_files:
                    latest_backup = max(backup_files)
                    self.run_command(f"sudo cp {latest_backup} {self.ssh_config_path}", shell=True)
            except:
                pass
            return False
        print()

        # 5. 重启SSH服务
        if not self.restart_ssh_service():
            print("❌ SSH服务重启失败")
            return False
        print()

        # 6. 检查防火墙
        self.check_firewall()
        print()

        # 7. 测试SFTP连接
        if self.test_sftp_connection():
            print()
            print("🎉 SFTP配置修复成功！")
            print("✅ SFTP子系统现在应该可以正常工作")
            print("✅ 可以重新尝试使用SFTP上传功能")
            return True
        else:
            print()
            print("⚠️ SFTP配置已完成，但连接测试失败")
            print("可能需要检查以下问题：")
            print("1. 防火墙设置")
            print("2. SELinux配置")
            print("3. SSH客户端配置")
            print("4. 网络连接问题")
            return False


def main():
    """主函数"""
    fixer = SftpConfigFixer()

    # 检查是否有sudo权限
    if not fixer.check_sudo_permission():
        print("❌ 错误：需要sudo无密码权限")
        print("请先运行sudo无密码设置脚本")
        sys.exit(1)

    # 执行修复
    success = fixer.fix_sftp()
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
