#!/usr/bin/env python3
"""
一键设置sudo无密码脚本
用途: 为指定用户设置sudo无密码权限
用法: python setup_sudo_nopasswd.py [用户名] [密码]
"""

import os
import sys
import subprocess
import tempfile
import argparse
from datetime import datetime


class SudoNopasswdSetup:
    """sudo无密码设置器"""
    
    def __init__(self, username=None, password=None):
        # 参数优先级：传入参数 > 环境变量 > 默认值
        self.username = username or os.environ.get('SSH_USERNAME', 'mxhou')
        self.password = password or os.environ.get('SSH_PASSWORD', 'zyc')
        self.sudoers_path = "/etc/sudoers"
    
    def run_command_with_sudo(self, cmd, shell=False):
        """使用sudo密码运行命令"""
        try:
            if isinstance(cmd, str) and not shell:
                cmd = cmd.split()
            
            # 使用echo传递密码给sudo -S
            if shell:
                full_cmd = f"echo '{self.password}' | sudo -S {cmd}"
            else:
                full_cmd = f"echo '{self.password}' | sudo -S " + " ".join(cmd)
            
            result = subprocess.run(full_cmd, shell=True, capture_output=True, 
                                  text=True, check=True)
            return result
        except subprocess.CalledProcessError as e:
            raise Exception(f"命令执行失败: {e.stderr}")
    
    def run_command(self, cmd, check=True, capture_output=True, shell=False):
        """运行普通命令"""
        try:
            if isinstance(cmd, str) and not shell:
                cmd = cmd.split()
            result = subprocess.run(cmd, capture_output=capture_output, 
                                  text=True, check=check, shell=shell)
            return result
        except subprocess.CalledProcessError as e:
            if check:
                raise
            return e
    
    def check_user_exists(self):
        """检查用户是否存在"""
        try:
            result = self.run_command(f"id {self.username}", check=False)
            return result.returncode == 0
        except:
            return False
    
    def add_user_to_sudo_group(self):
        """添加用户到sudo组"""
        print(f"正在检查用户 {self.username} 是否在sudo组中...")
        
        try:
            result = self.run_command(f"groups {self.username}")
            if "sudo" in result.stdout:
                print(f"✓ 用户 {self.username} 已在sudo组中")
                return True
            else:
                print(f"正在将用户 {self.username} 添加到sudo组...")
                self.run_command_with_sudo(f"usermod -aG sudo {self.username}")
                print(f"✓ 用户 {self.username} 已添加到sudo组")
                return True
        except Exception as e:
            print(f"❌ 添加用户到sudo组失败: {e}")
            return False
    
    def configure_sudoers(self):
        """配置sudoers文件"""
        print("正在配置sudoers文件...")
        
        try:
            # 备份原始sudoers文件
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_path = f"{self.sudoers_path}.backup.{timestamp}"
            self.run_command_with_sudo(f"cp {self.sudoers_path} {backup_path}")
            print("✓ 已备份原始sudoers文件")
            
            # 检查是否已经存在该用户的NOPASSWD配置
            result = self.run_command_with_sudo(f"grep '^{self.username}.*NOPASSWD' {self.sudoers_path}")
            if result.returncode == 0:
                print(f"✓ 用户 {self.username} 的NOPASSWD配置已存在")
                return True
            
            # 创建临时文件
            with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.sudoers') as temp_file:
                temp_path = temp_file.name
                
                # 读取现有sudoers内容
                result = self.run_command_with_sudo(f"cat {self.sudoers_path}")
                temp_file.write(result.stdout)
                
                # 添加NOPASSWD配置
                temp_file.write(f"\n")
                temp_file.write(f"# Allow {self.username} to run sudo without password\n")
                temp_file.write(f"{self.username} ALL=(ALL) NOPASSWD: ALL\n")
            
            # 验证sudoers文件语法
            try:
                self.run_command_with_sudo(f"visudo -c -f {temp_path}")
                print("✓ sudoers文件语法检查通过")
                
                # 应用新配置
                self.run_command_with_sudo(f"cp {temp_path} {self.sudoers_path}")
                print("✓ sudoers文件已更新")
                
                # 清理临时文件
                os.unlink(temp_path)
                return True
            except Exception as e:
                print(f"❌ sudoers文件语法错误，配置失败: {e}")
                os.unlink(temp_path)
                return False
                
        except Exception as e:
            print(f"❌ 配置sudoers文件失败: {e}")
            return False
    
    def test_sudo_config(self):
        """测试sudo无密码配置"""
        print("正在测试sudo无密码配置...")
        
        try:
            # 测试当前用户的sudo无密码
            result = self.run_command(f"sudo -n -u {self.username} sudo -n whoami", check=False)
            if result.returncode == 0:
                print(f"✓ sudo无密码配置成功！用户 {self.username} 可以无密码使用sudo")
                return True
            else:
                print("⚠ 配置已完成，可能需要重新登录才能生效")
                return False
        except Exception as e:
            print(f"⚠ 测试sudo配置时出现异常: {e}")
            print("⚠ 配置已完成，可能需要重新登录才能生效")
            return False
    
    def setup(self):
        """主设置流程"""
        print("=== Ubuntu sudo无密码设置脚本 ===")
        print(f"目标用户: {self.username}")
        
        # 显示参数来源
        if os.environ.get('SSH_USERNAME'):
            print("✓ 使用环境变量 SSH_USERNAME")
        else:
            print("⚠ 使用默认用户名")
        
        if os.environ.get('SSH_PASSWORD'):
            print("✓ 使用环境变量 SSH_PASSWORD")
        else:
            print("⚠ 使用默认密码")
        print()
        
        # 参数验证
        if not self.username or not self.password:
            print("❌ 错误：用户名和密码不能为空")
            return False
        
        # 检查用户是否存在
        if not self.check_user_exists():
            print(f"❌ 错误：用户 {self.username} 不存在")
            print("请先创建用户或修改脚本中的用户名")
            return False
        
        print("开始配置过程...")
        print("使用密码自动认证模式...")
        print()
        
        # 执行配置步骤
        if not self.add_user_to_sudo_group():
            return False
        print()
        
        if not self.configure_sudoers():
            return False
        print()
        
        # 测试配置
        success = self.test_sudo_config()
        print()
        
        if success:
            print("=== 配置完成 ===")
            print(f"✓ 用户 {self.username} 现在可以无密码使用sudo命令")
            print("✓ 原始sudoers文件已备份")
            print()
            print("测试命令: sudo whoami")
            print("注意：可能需要重新登录或开启新终端窗口才能生效")
        else:
            print("=== 配置完成 ===")
            print(f"✓ 用户 {self.username} 的sudo无密码配置已添加")
            print("✓ 原始sudoers文件已备份")
            print("⚠ 可能需要重新登录或开启新终端窗口才能生效")
        
        return True


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="Ubuntu sudo无密码设置脚本",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例:
  %(prog)s                           # 使用环境变量或默认值
  %(prog)s mxhou zyc                # 使用指定的用户名和密码
  SSH_USERNAME=user SSH_PASSWORD=pass %(prog)s  # 使用环境变量

环境变量:
  SSH_USERNAME    SSH用户名
  SSH_PASSWORD    SSH密码
        """
    )
    
    parser.add_argument('username', nargs='?', help='要设置sudo无密码的用户名（可选）')
    parser.add_argument('password', nargs='?', help='当前用户的sudo密码（可选）')
    
    args = parser.parse_args()
    
    # 创建设置器并执行
    setup = SudoNopasswdSetup(args.username, args.password)
    success = setup.setup()
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
