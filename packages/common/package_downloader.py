#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的包下载管理器
入口函数接收在线下载链接，检查在线版本，检查gitea是否存在，
不存在的话，在线下载并上传，存在的话，从gitea下载
"""

import os
import logging
import tempfile
from typing import Optional, Tuple, Callable, TYPE_CHECKING

if TYPE_CHECKING:
    from gitea_package_manager import GiteaPackageManager

# 导入gitea包管理器
try:
    from .gitea_package_manager import GiteaPackageManager, DEFAULT_GITEA_CONFIG
    GITEA_AVAILABLE = True
except ImportError:
    GITEA_AVAILABLE = False
    GiteaPackageManager = None
    DEFAULT_GITEA_CONFIG = None
    logging.warning("Gitea包管理器不可用，将使用传统下载方式")

def download_package(package_name: str,
                    online_downloader: Callable[[str, bool], Tuple[Optional[str], Optional[str]]],
                    package_type: str = "generic",
                    gitea_config: dict = None) -> Tuple[Optional[str], str]:
    """
    简化的包下载入口函数

    Args:
        package_name: 包名
        online_downloader: 在线下载函数，接收(temp_dir, download_now)参数，返回(version, file_path)元组
                          download_now=False时只返回版本信息，download_now=True时执行下载
        package_type: 包类型 (generic, deb, rpm, zip, tar.gz, exe, appimage等)
        gitea_config: Gitea配置，如果为None则使用内置默认配置

    Returns:
        (file_path, filename): 下载后的包路径及名称
    """
    # 初始化Gitea管理器（如果没有提供配置，将使用内置默认配置）
    gitea_manager = _init_gitea_manager(gitea_config)

    # 创建临时目录
    temp_dir = tempfile.mkdtemp(prefix=f"{package_name}_")

    try:
        # 1. 先获取版本信息（不下载）
        logging.info(f"获取 {package_name} 的版本信息...")
        online_version, _ = online_downloader(temp_dir, download_now=False)

        if not online_version:
            logging.error("无法获取在线版本")
            return None, ""

        logging.info(f"在线版本: {online_version}")

        # 2. 检查Gitea是否存在该包
        gitea_exists = _check_gitea_package_exists(gitea_manager, package_name, package_type)

        if gitea_exists:
            # 3. 优先从Gitea下载
            logging.info(f"从Gitea下载 {package_name}")
            gitea_file_path = _download_from_gitea(gitea_manager, package_name, online_version, temp_dir, package_type)
            if gitea_file_path:
                filename = os.path.basename(gitea_file_path)
                logging.info(f"从Gitea下载成功: {filename}")
                return gitea_file_path, filename
            else:
                logging.warning("从Gitea下载失败，尝试在线下载")

        # 4. 在线下载
        logging.info(f"从在线下载 {package_name}")
        _, file_path = online_downloader(temp_dir, download_now=True)

        if not file_path or not os.path.exists(file_path):
            logging.error("在线下载失败")
            return None, ""

        filename = os.path.basename(file_path)
        logging.info(f"在线下载成功: {filename}")

        # 5. 上传到Gitea
        if gitea_manager:
            logging.info(f"上传 {package_name} 到Gitea")
            _upload_to_gitea(gitea_manager, package_name, online_version, file_path, package_type)

        return file_path, filename

    except Exception as e:
        logging.error(f"下载过程出错: {e}")
        return None, ""


def _init_gitea_manager(gitea_config: dict = None) -> Optional['GiteaPackageManager']:
    """初始化Gitea包管理器"""
    if not GITEA_AVAILABLE:
        return None

    try:
        # 如果没有提供配置，使用默认配置（内置在gitea_package_manager中）
        if gitea_config:
            manager = GiteaPackageManager(**gitea_config)
        else:
            # 使用默认配置创建管理器
            manager = GiteaPackageManager.create_default()

        if manager.test_connection():
            logging.info("Gitea连接成功")
            return manager
        else:
            logging.warning("Gitea连接测试失败")
            return None
    except Exception as e:
        logging.warning(f"初始化Gitea包管理器失败: {e}")
        return None


def _check_gitea_package_exists(gitea_manager: Optional['GiteaPackageManager'],
                               package_name: str,
                               package_type: str) -> bool:
    """检查Gitea中是否存在指定包"""
    if not gitea_manager:
        return False

    try:
        # 简化逻辑：所有文件格式都作为generic类型存储
        search_type = 'generic'  # 统一使用generic类型查找

        packages = gitea_manager.list_packages(package_type=search_type)
        for package in packages:
            if package.get('name') == package_name:
                logging.info(f"在{search_type}类型中找到包: {package_name}")
                return True

        return False
    except Exception as e:
        logging.warning(f"检查Gitea包存在性失败: {e}")
        return False

def _download_from_gitea(gitea_manager: Optional['GiteaPackageManager'],
                        package_name: str,
                        version: str,
                        temp_dir: str,
                        package_type: str) -> Optional[str]:
    """从Gitea下载包"""
    if not gitea_manager:
        return None

    try:
        # 直接使用常见的文件名格式（避免不必要的API调用）
        possible_filenames = []

        # 通用的标准化文件名格式
        # 由于上传时已经标准化，优先尝试标准格式
        if package_type == "deb":
            ext = ".deb"
        elif package_type == "rpm":
            ext = ".rpm"
        elif package_type == "zip":
            ext = ".zip"
        elif package_type in ["tar", "tar.gz", "tgz"]:
            ext = ".tar.gz"
        elif package_type == "tar.xz":
            ext = ".tar.xz"
        elif package_type == "exe":
            ext = ".exe"
        elif package_type == "appimage":
            ext = ".AppImage"
        else:
            ext = ""

        # 标准化文件名格式（上传时使用的格式）
        possible_filenames = [
            f"{package_name}_{version}{ext}",  # 主要格式
            f"{package_name}-{version}{ext}",  # 备用格式
            f"{package_name}{ext}"             # 无版本格式
        ]

        # 为AppImage添加特殊格式
        if package_type == "appimage":
            possible_filenames.extend([
                f"{package_name}-{version}-linux.AppImage",
                f"{package_name}_{version}.AppImage",
                f"{package_name}-{version}.AppImage"
            ])
        else:
            # 对于generic类型，尝试多种可能的扩展名
            possible_filenames = [
                f"{package_name}_{version}",                 # 无扩展名格式（如 planet 文件）
                f"{package_name}-{version}",                 # 无扩展名格式
                f"{package_name}",                           # 纯包名无扩展名
                f"{package_name}-{version}-linux.AppImage",  # 思源笔记 AppImage 格式
                f"{package_name}_{version}.AppImage",        # 通用 AppImage 格式
                f"{package_name}-{version}.AppImage",        # 通用 AppImage 格式
                f"{package_name}_{version}_amd64.deb",       # Docker deb包格式
                f"{package_name}_{version}.deb",
                f"{package_name}_{version}.bundle",          # VMware bundle 格式
                f"{package_name}_{version}.txt",             # 测试文件使用的格式
                f"{package_name}_{version}.tar.xz",          # Firefox 等 tar.xz 格式
                f"{package_name}_{version}.tar.gz",
                f"{package_name}_{version}.tar.bz2",
                f"{package_name}_{version}.zip",
                f"{package_name}-{version}.tar.xz",
                f"{package_name}-{version}.tar.gz",
                f"{package_name}-{version}.tar.bz2",
                f"{package_name}-{version}.zip",
                f"{package_name}.tar.xz",
                f"{package_name}.tar.gz",
                f"{package_name}.tar.bz2",
                f"{package_name}.zip"
            ]

        logging.info(f"从Gitea下载 {package_name} v{version}")

        # 尝试每个可能的文件名
        for filename in possible_filenames:
            local_path = os.path.join(temp_dir, filename)

            try:
                # 简化逻辑：统一使用generic类型下载
                download_type = 'generic'
                success = gitea_manager.download(
                    package_type=download_type,
                    package_name=package_name,
                    version=version,
                    filename=filename,
                    local_path=local_path
                )

                if success and os.path.exists(local_path):
                    logging.info(f"从Gitea下载成功: {local_path}")
                    return local_path

            except Exception as e:
                logging.debug(f"尝试文件名 {filename} 失败: {e}")
                continue

        logging.warning("从Gitea下载失败 - 尝试了所有可能的文件名")
        return None

    except Exception as e:
        logging.error(f"从Gitea下载异常: {e}")
        return None

def _upload_to_gitea(gitea_manager: Optional['GiteaPackageManager'],
                    package_name: str,
                    version: str,
                    file_path: str,
                    package_type: str) -> bool:
    """上传包到Gitea"""
    if not gitea_manager:
        return False

    try:
        original_filename = os.path.basename(file_path)

        # 生成标准化的文件名，正确处理复合扩展名
        if original_filename.endswith('.tar.xz'):
            file_ext = '.tar.xz'
        elif original_filename.endswith('.tar.gz'):
            file_ext = '.tar.gz'
        elif original_filename.endswith('.tar.bz2'):
            file_ext = '.tar.bz2'
        elif original_filename.endswith('.bundle'):
            file_ext = '.bundle'
        else:
            file_ext = os.path.splitext(original_filename)[1]  # 获取扩展名，如 .deb

        standardized_filename = f"{package_name}_{version}{file_ext}"

        logging.info(f"上传 {package_name} v{version} 到Gitea")
        logging.info(f"原始文件名: {original_filename}")
        logging.info(f"标准化文件名: {standardized_filename}")

        # 使用标准化文件名上传
        success = gitea_manager.upload(
            package_type=package_type,
            package_name=package_name,
            version=version,
            file_path=file_path,
            filename=standardized_filename  # 使用标准化文件名
        )

        if success:
            logging.info("上传到Gitea成功")
            return True
        else:
            logging.warning("上传到Gitea失败")
            return False

    except Exception as e:
        logging.error(f"上传到Gitea异常: {e}")
        return False

# ==================== 简单的Docker下载示例 ====================

import urllib.request

def download_docker_deb(temp_dir, download_now=True):
    """下载Docker deb包 - 备用方式"""
    version = "24.0.7"  # 当前版本

    # 如果只需要版本信息，直接返回
    if not download_now:
        print(f"📋 获取Docker版本信息: {version}")
        return version, None

    # 定义下载链接
    download_urls = [
        f"https://download.docker.com/linux/ubuntu/dists/jammy/pool/stable/amd64/docker-ce_{version}-1~ubuntu.22.04~jammy_amd64.deb",
        f"https://download.docker.com/linux/ubuntu/dists/focal/pool/stable/amd64/docker-ce_{version}-1~ubuntu.20.04~focal_amd64.deb",
        f"https://download.docker.com/linux/static/stable/x86_64/docker-{version}.tgz"
    ]

    try:
        for i, url in enumerate(download_urls):
            try:
                if i < 2:  # deb包
                    filename = f"docker-ce_{version}_amd64.deb"
                else:  # tgz包
                    filename = f"docker-{version}.tgz"

                file_path = os.path.join(temp_dir, filename)

                print(f"📥 尝试下载Docker {version} (方式{i+1})...")
                urllib.request.urlretrieve(url, file_path)

                if os.path.exists(file_path) and os.path.getsize(file_path) > 0:
                    print(f"✅ 下载成功: {filename}")
                    return version, file_path

            except Exception as e:
                print(f"❌ 方式{i+1}失败: {e}")
                continue

        return version, None
    except Exception as e:
        print(f"❌ 下载失败: {e}")
        return None, None


# 使用示例
if __name__ == "__main__":
    # 配置日志
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

    print("🐳 Docker安装示例")
    print("=" * 40)

    downloader = download_docker_deb

    # 调用下载/安装函数
    file_path, filename = download_package(
        package_name="docker-ce",
        online_downloader=downloader,
        package_type="generic"
    )

    if file_path:
        print(f"🎉 成功! 文件: {filename}")
        print(f"📁 路径: {file_path}")
    else:
        print("💥 失败!")
