#!/usr/bin/env python3
"""
检查所有软件包状态的脚本 - 多线程版本
"""

import os
import subprocess
import json
import threading
import time
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed

def check_package_status(package_info):
    """检查单个包的状态 - 多线程版本"""
    package_file = package_info['file']
    package_type = package_info['type']
    package_name = package_info['name']

    try:
        if package_type == 'python':
            cmd = f'python3 packages/{package_file} status'
        else:
            cmd = f'bash packages/{package_file} status'

        # 设置合理的超时时间，避免长时间等待
        # 对于可能包含硬件检测的包，使用较短的超时时间
        timeout = 10 if 'driver' in package_name.lower() or 'graphic' in package_name.lower() else 15

        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=timeout)

        if result.returncode == 0:
            output = result.stdout.strip().lower()
            if 'running' in output or 'active' in output:
                status = 'running'
            elif 'installed' in output or 'stopped' in output:
                status = 'installed'
            else:
                status = 'installed'
        else:
            # 检查stderr是否包含超时相关信息
            stderr_output = result.stderr.strip().lower()
            if 'timeout' in stderr_output or 'time out' in stderr_output:
                status = 'timeout'
            else:
                status = 'failed'
    except subprocess.TimeoutExpired:
        status = 'timeout'
    except Exception as e:
        status = 'unknown'

    # 返回包信息和状态
    return {
        'package': package_info,
        'status': status,
        'name': package_name
    }

def main():
    start_time = datetime.now()
    print('🔍 开始检查所有软件包状态...', flush=True)
    print(f'⏰ 开始时间: {start_time.strftime("%Y-%m-%d %H:%M:%S")}', flush=True)
    print('=' * 50, flush=True)

    # 获取所有包文件
    packages_dir = 'packages'
    if not os.path.exists(packages_dir):
        print('❌ packages目录不存在')
        return

    # 扫描所有包文件
    all_packages = []
    for root, dirs, files in os.walk(packages_dir):
        # 跳过 common 目录
        if 'common' in dirs:
            dirs.remove('common')

        for file in files:
            if file.endswith('.py') or file.endswith('.sh'):
                rel_path = os.path.relpath(os.path.join(root, file), packages_dir)

                # 确保不处理 common 目录下的文件
                if rel_path.startswith('common/'):
                    continue

                package_type = 'python' if file.endswith('.py') else 'shell'
                all_packages.append({
                    'file': rel_path,
                    'name': file.replace('.py', '').replace('.sh', ''),
                    'type': package_type
                })

    print(f'📦 找到 {len(all_packages)} 个软件包', flush=True)
    print('-' * 30, flush=True)

    # 多线程检查每个包的状态
    installed_packages = []
    timeout_count = 0
    completed_count = 0

    # 使用线程锁保护共享变量
    lock = threading.Lock()

    def update_progress(result):
        nonlocal completed_count, timeout_count
        with lock:
            completed_count += 1
            status = result['status']
            package_name = result['name']
            pkg = result['package']

            print(f'[{completed_count}/{len(all_packages)}] {package_name} - ', end='', flush=True)

            if status == 'timeout':
                timeout_count += 1
                print(f'⏰ 检查超时', flush=True)
            elif status in ['running', 'installed', 'stopped']:
                pkg['status'] = status
                pkg['lastChecked'] = datetime.now().isoformat()
                installed_packages.append(pkg)
                print(f'✅ {status}', flush=True)
            else:
                print(f'⚪ 未安装', flush=True)

    # 使用线程池执行检查，最大并发数为8
    max_workers = min(8, len(all_packages))
    print(f'🚀 使用 {max_workers} 个线程并发检查...', flush=True)

    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # 提交所有检查任务
        future_to_package = {executor.submit(check_package_status, pkg): pkg for pkg in all_packages}

        # 处理完成的任务
        for future in as_completed(future_to_package):
            try:
                result = future.result()
                update_progress(result)
            except Exception as e:
                with lock:
                    completed_count += 1
                    pkg = future_to_package[future]
                    print(f'[{completed_count}/{len(all_packages)}] {pkg["name"]} - ❌ 检查异常: {str(e)}', flush=True)

    print('-' * 30, flush=True)

    # 计算检查用时
    end_time = datetime.now()
    duration = end_time - start_time
    duration_ms = int(duration.total_seconds() * 1000)

    print(f'🎉 多线程检查完成！', flush=True)
    print(f'⏰ 结束时间: {end_time.strftime("%Y-%m-%d %H:%M:%S")}', flush=True)
    print(f'⏱️  检查用时: {duration_ms}ms ({duration.total_seconds():.2f}秒)', flush=True)
    print(f'🧵 并发线程: {max_workers}', flush=True)
    print(f'📊 总包数: {len(all_packages)}', flush=True)
    print(f'📦 已安装: {len(installed_packages)}', flush=True)
    print(f'⚪ 未安装: {len(all_packages) - len(installed_packages) - timeout_count}', flush=True)
    if timeout_count > 0:
        print(f'⏰ 超时包数: {timeout_count}', flush=True)

    # 保存已安装包列表到文件
    installed_file = 'installed_packages.json'
    try:
        with open(installed_file, 'w', encoding='utf-8') as f:
            json.dump({
                'packages': installed_packages,
                'total_packages': len(all_packages),
                'installed_count': len(installed_packages),
                'timeout_count': timeout_count,
                'uninstalled_count': len(all_packages) - len(installed_packages) - timeout_count,
                'last_check': end_time.isoformat(),
                'check_duration_ms': duration_ms,
                'check_duration_seconds': round(duration.total_seconds(), 2),
                'max_workers': max_workers,
                'check_method': 'multithreaded'
            }, f, indent=2, ensure_ascii=False)
        print(f'💾 已保存检查结果到 {installed_file}', flush=True)
    except Exception as e:
        print(f'⚠️ 保存结果失败: {e}', flush=True)

    print('=' * 50, flush=True)

if __name__ == '__main__':
    main()
