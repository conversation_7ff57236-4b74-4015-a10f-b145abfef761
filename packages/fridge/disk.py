#!/usr/bin/env python3
"""
磁盘管理和自动挂载工具
支持平台: Ubuntu/Debian
架构: 通用
"""

import os
import sys
import subprocess
import pwd
import time

current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
sys.path.insert(0, project_root)

def get_user_info():
    sudo_user = os.environ.get('SUDO_USER')
    if sudo_user:
        try:
            user_info = pwd.getpwnam(sudo_user)
            return user_info.pw_name, user_info.pw_dir, user_info.pw_uid, user_info.pw_gid
        except KeyError:
            pass
    return "mxhou", "/home/<USER>", 1000, 1000

def run_command_sync(command, description="", capture_output=False, silent=False):
    if not silent:
        print("-" * 50)
        if description:
            print(f"🔄 {description}")
        print(f"执行: {command}")

    if capture_output:
        result = subprocess.run(command, shell=True, check=False, capture_output=True, text=True)
        if result.stderr.strip() and not silent:
            print(f"错误: {result.stderr.strip()}")
    else:
        result = subprocess.run(command, shell=True, check=False)

    if not silent:
        if result.returncode == 0:
            print(f"✅ 完成: {description}")
        else:
            print(f"⚠️ 失败: {description} (返回码: {result.returncode})")
        print("-" * 50)
        time.sleep(1)

    return result

def _create_autofs_script():
    import tempfile
    import glob
    import re

    result = run_command_sync("which ntfs-3g", "检查ntfs-3g", capture_output=True, silent=True)
    if result.returncode != 0:
        print("警告：ntfs-3g未安装，NTFS文件系统可能无法正常挂载")

    autofs_entries = []
    device_patterns = ['/dev/sd*', '/dev/nvme*']
    devices = []

    for pattern in device_patterns:
        devices.extend(glob.glob(pattern))

    partition_devices = [dev for dev in devices if re.search(r'\d+$', dev)]

    for device in partition_devices:
        try:
            uuid_result = run_command_sync(
                f"sudo blkid -o value -s UUID {device}",
                f"获取设备UUID {device}", capture_output=True, silent=True
            )

            type_result = run_command_sync(
                f"sudo blkid -o value -s TYPE {device}",
                f"获取文件系统类型 {device}", capture_output=True, silent=True
            )

            if uuid_result.returncode == 0 and type_result.returncode == 0:
                uuid = uuid_result.stdout.strip()
                fs_type = type_result.stdout.strip()

                if uuid and fs_type:
                    dev_name = os.path.basename(device)
                    mount_opts = "rw,sync,noatime,users,exec"

                    if fs_type == "ntfs":
                        mount_opts = "rw,sync,noatime,users,exec,umask=0022,uid=1000,gid=1000,fmask=0133,dmask=0022,nofail"
                    elif fs_type in ["ext4", "ext3", "ext2"]:
                        mount_opts = "rw,sync,noatime,users,exec,nofail,errors=remount-ro"
                    else:
                        mount_opts = "rw,sync,noatime,users,exec,nofail"

                    autofs_entry = f"{dev_name} -fstype={fs_type},{mount_opts} :/{device}"
                    autofs_entries.append(autofs_entry)

        except:
            continue

    script_content = "#!/bin/bash\n# 自动挂载可移动设备的脚本\n\n"
    script_content += "# autofs配置条目\n"
    for entry in autofs_entries:
        script_content += f"echo '{entry}'\n"

    if not autofs_entries:
        script_content += "# 未找到可挂载的设备\n"

    with tempfile.NamedTemporaryFile(mode='w', suffix='.sh', delete=False) as f:
        f.write(script_content)
        temp_path = f.name

    run_command_sync(f"sudo cp {temp_path} /etc/auto.removable", "复制autofs脚本", capture_output=True)
    run_command_sync("sudo chmod +x /etc/auto.removable", "设置脚本权限", capture_output=True)
    run_command_sync(f"rm -f {temp_path}", "清理临时文件", capture_output=True)
    print("autofs脚本创建完成")

def _configure_fstab():
    import re
    import tempfile

    username, _, uid, gid = get_user_info()

    run_command_sync("sudo cp /etc/fstab /etc/fstab.backup", "备份fstab文件", capture_output=True)

    blkid_result = run_command_sync("sudo blkid", "获取设备信息", capture_output=True, silent=True)
    blkid_output = blkid_result.stdout

    devices = []
    for line in blkid_output.splitlines():
        if ('TYPE="squashfs"' not in line and
            ('TYPE="ext' in line or 'TYPE="ntfs"' in line or 'TYPE="xfs"' in line or 'TYPE="btrfs"' in line) and
            '/dev/loop' not in line):

            device_path = line.split(':')[0]

            grep_result = run_command_sync(f"grep {device_path} /etc/fstab",
                                       f"检查fstab中的设备 {device_path}", capture_output=True, silent=True)

            mount_result = run_command_sync(f"findmnt -n -o TARGET {device_path}",
                                        f"检查设备挂载点 {device_path}", capture_output=True, silent=True)

            is_system_partition = False
            if mount_result.returncode == 0:
                mount_points = mount_result.stdout.strip().split('\n')
                for mp in mount_points:
                    if mp in ['/', '/boot', '/boot/efi']:
                        is_system_partition = True
                        break

            if grep_result.returncode != 0 and not is_system_partition:
                uuid_match = re.search(r'UUID="([^"]+)"', line)
                type_match = re.search(r'TYPE="([^"]+)"', line)
                label_match = re.search(r'LABEL="([^"]+)"', line)

                if uuid_match and type_match:
                    uuid = uuid_match.group(1)
                    fs_type = type_match.group(1)
                    label = label_match.group(1) if label_match else ''

                    if fs_type not in ['swap', 'squashfs']:
                        devices.append((device_path, uuid, fs_type, label))

    for device, uuid, fs_type, label in devices:
        if label:
            mount_point = f'/media/{username}/{label.lower()}'
        else:
            dev_name = os.path.basename(device)
            mount_point = f'/media/{username}/{dev_name}'

        run_command_sync(f"sudo mkdir -p {mount_point}", f"创建挂载点 {mount_point}", capture_output=True)
        run_command_sync(f"sudo chown {username}:{username} {mount_point}", f"设置挂载点所有者", capture_output=True)
        run_command_sync(f"sudo chmod 755 {mount_point}", f"设置挂载点权限", capture_output=True)

        if fs_type == 'ntfs':
            mount_options = f'defaults,noatime,umask=0022,uid={uid},gid={gid},fmask=0133,dmask=0022,nofail,windows_names'
        elif fs_type.startswith('ext'):
            mount_options = 'defaults,noatime,nofail,errors=remount-ro'
        else:
            mount_options = 'defaults,noatime,nofail'

        fstab_entry = f'UUID={uuid} {mount_point} {fs_type} {mount_options} 0 2\n'

        run_command_sync(f"sudo sed -i '/UUID={uuid}/d' /etc/fstab", f"清理旧的fstab条目", capture_output=True)

        with tempfile.NamedTemporaryFile(mode='w', delete=False) as f:
            f.write(fstab_entry)
            temp_path = f.name

        run_command_sync(f"sudo bash -c 'cat {temp_path} >> /etc/fstab'", f"添加fstab条目", capture_output=True)
        run_command_sync(f"rm -f {temp_path}", "清理临时文件", capture_output=True)

        try:
            run_command_sync(f"sudo mount {mount_point}", f"挂载设备 {mount_point}", capture_output=True, silent=True)
            run_command_sync(f"sudo chmod 755 {mount_point}", f"设置挂载后权限", capture_output=True, silent=True)
            if fs_type.startswith('ext'):
                run_command_sync(f"sudo chown {username}:{username} {mount_point}", f"设置ext文件系统权限", capture_output=True, silent=True)
        except:
            pass

    print('fstab配置完成')

def _fix_disk_permissions():
    username, _, _, _ = get_user_info()

    mount_result = run_command_sync("mount", "获取挂载信息", capture_output=True, silent=True)
    mount_output = mount_result.stdout.strip().split('\n')

    user_mounts = []
    nfs_mounts = []

    for line in mount_output:
        if 'type nfs' in line:
            parts = line.split(' on ')
            if len(parts) >= 2:
                mount_path = parts[1].split(' ')[0]
                nfs_mounts.append(mount_path)
        elif f'/media/{username}/' in line or f'/mnt/{username}/' in line:
            parts = line.split(' on ')
            if len(parts) >= 2:
                mount_path = parts[1].split(' ')[0]
                if mount_path not in nfs_mounts:
                    user_mounts.append(mount_path)

    fast2t_path = f'/media/{username}/fast2t'
    if os.path.exists(fast2t_path) and fast2t_path not in user_mounts and fast2t_path not in nfs_mounts:
        user_mounts.append(fast2t_path)

    for mount_point in user_mounts:
        try:
            run_command_sync(f"sudo chmod 755 {mount_point}", f"修复权限 {mount_point}", capture_output=True, silent=True)
            run_command_sync(f"sudo chown {username}:{username} {mount_point}", f"修复所有者 {mount_point}", capture_output=True, silent=True)
        except:
            pass

    print('磁盘权限修复完成')

def _create_symlinks():
    username, user_home, _, _ = get_user_info()

    download_target = f'/media/{username}/download'
    home_download = os.path.join(user_home, '下载')

    if os.path.exists(download_target) and not os.path.exists(home_download):
        try:
            os.symlink(download_target, home_download)
            print(f'已创建符号链接: {home_download} -> {download_target}')
        except:
            pass

def _update_bookmarks():
    import tempfile

    username, user_home, _, _ = get_user_info()

    mount_result = run_command_sync("mount", "获取挂载信息", capture_output=True, silent=True)
    mount_output = mount_result.stdout.strip().split('\n')
    mount_points = []

    for line in mount_output:
        if f'/media/{username}/' in line:
            parts = line.split(' on ')
            if len(parts) >= 2:
                mount_path = parts[1].split(' ')[0]
                mount_points.append(mount_path)

    fast2t_path = f'/media/{username}/fast2t'
    if os.path.exists(fast2t_path) and fast2t_path not in mount_points:
        mount_points.append(fast2t_path)

    gtk_bookmarks_file = f'{user_home}/.config/gtk-3.0/bookmarks'
    os.makedirs(os.path.dirname(gtk_bookmarks_file), exist_ok=True)

    existing_bookmarks = []
    if os.path.exists(gtk_bookmarks_file):
        try:
            with open(gtk_bookmarks_file, 'r') as f:
                existing_bookmarks = [line.strip() for line in f.readlines()]
        except:
            pass

    existing_bookmarks_set = set(existing_bookmarks)
    for mount_point in mount_points:
        mount_name = os.path.basename(mount_point)
        bookmark_uri = f'file://{mount_point} {mount_name}'
        if bookmark_uri not in existing_bookmarks_set:
            existing_bookmarks.append(bookmark_uri)
            existing_bookmarks_set.add(bookmark_uri)

    with tempfile.NamedTemporaryFile(mode='w', delete=False) as f:
        f.write('\n'.join(existing_bookmarks) + '\n')
        temp_path = f.name

    run_command_sync(f"sudo cp {temp_path} {gtk_bookmarks_file}", "更新书签文件", capture_output=True)
    run_command_sync(f"sudo chown {username}:{username} {gtk_bookmarks_file}", "设置书签文件所有者", capture_output=True)
    run_command_sync(f"sudo chmod 644 {gtk_bookmarks_file}", "设置书签文件权限", capture_output=True)
    run_command_sync(f"rm -f {temp_path}", "清理临时文件", capture_output=True)

    print('文件管理器书签更新完成')

def _unmount_user_disks():
    username, _, _, _ = get_user_info()

    mount_result = run_command_sync("mount", "获取挂载信息", capture_output=True, silent=True)
    mount_output = mount_result.stdout.strip().split('\n')
    user_mounts = []

    for line in mount_output:
        if f'/media/{username}/' in line or f'/mnt/{username}/' in line:
            parts = line.split(' on ')
            if len(parts) >= 2:
                mount_path = parts[1].split(' ')[0]
                user_mounts.append(mount_path)

    for mount_point in user_mounts:
        try:
            result = run_command_sync(f"sudo umount {mount_point}", f"卸载 {mount_point}", capture_output=True, silent=True)
            if result.returncode == 0:
                print(f'已卸载: {mount_point}')
        except:
            pass

def install():
    status_code = check_status()
    if status_code != 2:
        uninstall()

    username, _, _, _ = get_user_info()

    print("📥 安装磁盘管理和自动挂载...")

    run_command_sync("sudo apt-get update", "更新包列表", capture_output=True)
    run_command_sync("sudo apt-get install -y autofs ntfs-3g nfs-common", "安装磁盘管理工具", capture_output=True)

    run_command_sync("sudo mkdir -p /etc/auto.master.d", "创建autofs配置目录", capture_output=True)

    with open("/tmp/auto.removable.conf", "w") as f:
        f.write("# 可移动设备自动挂载配置\n")
        f.write("/media /etc/auto.removable --timeout=5 --ghost\n")

    run_command_sync("sudo cp /tmp/auto.removable.conf /etc/auto.master.d/auto.removable.conf", "复制autofs配置", capture_output=True)
    run_command_sync("rm -f /tmp/auto.removable.conf", "清理临时配置文件", capture_output=True)

    _create_autofs_script()
    _configure_fstab()
    _fix_disk_permissions()

    run_command_sync(f"sudo mkdir -p /media/{username}", "创建用户媒体目录", capture_output=True)
    run_command_sync(f"sudo chown {username}:{username} /media/{username}", "设置媒体目录所有者", capture_output=True)
    run_command_sync(f"sudo chmod 755 /media/{username}", "设置媒体目录权限", capture_output=True)

    _create_symlinks()
    _update_bookmarks()

    run_command_sync("sudo systemctl enable autofs", "启用autofs服务", capture_output=True)
    run_command_sync("sudo systemctl restart autofs", "重启autofs服务", capture_output=True)

    run_command_sync("sudo mount -a", "挂载所有设备", capture_output=True)

    print("✅ 磁盘管理和自动挂载配置完成")
    return True

def uninstall():
    print("🔄 开始卸载磁盘管理和自动挂载...")

    _, user_home, _, _ = get_user_info()

    run_command_sync("sudo systemctl stop autofs", "停止autofs服务", capture_output=True)
    run_command_sync("sudo systemctl disable autofs", "禁用autofs服务", capture_output=True)

    print("🔄 卸载用户挂载点...")
    _unmount_user_disks()

    run_command_sync("sudo cp /etc/fstab.backup /etc/fstab", "恢复fstab备份", capture_output=True)

    run_command_sync("sudo rm -f /etc/auto.master.d/auto.removable.conf", "清理autofs主配置", capture_output=True)
    run_command_sync("sudo rm -f /etc/auto.removable", "清理autofs脚本", capture_output=True)

    home_download = os.path.join(user_home, '下载')
    run_command_sync(f"rm -f {home_download}", "清理符号链接", capture_output=True)

    run_command_sync("sudo apt-get purge -y autofs", "卸载autofs包", capture_output=True)
    run_command_sync("sudo apt-get autoremove -y", "清理依赖包", capture_output=True)

    print("✅ 卸载完成")
    return True

def check_status():
    try:
        result = run_command_sync("systemctl is-active autofs", capture_output=True, silent=True)
        autofs_active = result.stdout.strip() == "active"
    except:
        autofs_active = False

    config_exists = (os.path.exists("/etc/auto.master.d/auto.removable.conf") and
                    os.path.exists("/etc/auto.removable"))

    if autofs_active and config_exists:
        print('🟢 状态: 已安装')
        return 0
    else:
        print('🔴 状态: 未安装')
        return 2

if __name__ == "__main__":
    command = sys.argv[1] if len(sys.argv) > 1 else "install"

    if command == "uninstall" or command == "--uninstall":
        success = uninstall()
        sys.exit(0 if success else 1)
    elif command == "status":
        exit_code = check_status()
        sys.exit(exit_code)
    else:
        success = install()
        sys.exit(0 if success else 1)
