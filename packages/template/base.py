#!/usr/bin/env python3
"""
基础工具包
支持平台: Ubuntu/Debian
架构: 通用
"""

import sys
import subprocess

def install():
    status_code = check_status()
    if status_code != 2:
        uninstall()

    print("🔄 开始安装基础工具包...")

    commands = [
        "sudo apt-get update",
        "sudo apt-get install -y vim git htop tree build-essential apt-transport-https ca-certificates gnupg software-properties-common net-tools dnsutils unzip p7zip-full rar unrar gparted timeshift curl wget iputils-ping python3 python3-pip"
    ]

    for cmd in commands:
        print(f"执行: {cmd}")
        result = subprocess.run(cmd, shell=True, check=False)
        if result.returncode != 0:
            print(f"❌ 命令执行失败: {cmd}")
            return False

    print("✅ 基础工具包安装完成")
    return True

def uninstall():
    print("🔄 开始卸载基础工具包...")

    print("🔄 检查并卸载Snap版本...")
    subprocess.run("sudo snap remove vim git htop tree 2>/dev/null || true", shell=True, check=False)

    print("🔄 卸载APT安装的包...")
    subprocess.run("sudo apt-get purge -y vim git htop tree build-essential apt-transport-https gnupg software-properties-common net-tools dnsutils unzip p7zip-full rar unrar gparted timeshift curl wget iputils-ping python3-pip 2>/dev/null || true", shell=True, check=False)

    print("🔄 清理残余依赖...")
    subprocess.run("sudo apt-get autoremove -y 2>/dev/null || true", shell=True, check=False)

    print("✅ 卸载完成")
    return True

def check_status():
    core_packages = [
        'vim', 'git', 'htop', 'tree', 'build-essential',
        'curl', 'wget', 'unzip', 'p7zip-full', 'rar', 'unrar',
        'gparted', 'timeshift', 'dnsutils', 'net-tools', 'python3-pip'
    ]

    installed_count = 0
    for package in core_packages:
        try:
            result = subprocess.run(['dpkg-query', '-W', '-f=${Status}', package],
                                  capture_output=True, text=True, timeout=5)
            if 'install ok installed' in result.stdout:
                installed_count += 1
        except:
            pass

    total_packages = len(core_packages)

    if installed_count == total_packages:
        print("🟢 状态: 已安装")
        return 0
    elif installed_count > 0:
        print("🟡 状态: 部分已安装")
        return 1
    else:
        print("🔴 状态: 未安装")
        return 2

if __name__ == "__main__":
    command = sys.argv[1] if len(sys.argv) > 1 else "install"

    if command == "uninstall" or command == "--uninstall":
        success = uninstall()
        sys.exit(0 if success else 1)
    elif command == "status":
        exit_code = check_status()
        sys.exit(exit_code)
    else:
        success = install()
        sys.exit(0 if success else 1)
