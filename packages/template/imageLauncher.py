#!/usr/bin/env python3
"""
AppImageLauncher
支持平台: Ubuntu/Debian
架构: 通用
"""

import os
import sys
import subprocess

current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
sys.path.insert(0, project_root)
from packages.common import get_user_info, run_command_sync

def install():
    status_code = check_status()
    if status_code != 2:
        uninstall()

    print("🚀 开始安装AppImageLauncher...")

    run_command_sync("sudo apt-get install -y software-properties-common", "安装依赖包", capture_output=True)
    run_command_sync("sudo add-apt-repository -y ppa:appimagelauncher-team/stable", "添加PPA源")
    run_command_sync("sudo apt update", "更新包列表", capture_output=True)
    run_command_sync("sudo apt install -y appimagelauncher", "安装AppImageLauncher", capture_output=True)

    _, user_home, uid, gid = get_user_info()
    app_dir = os.path.join(user_home, "Applications")
    os.makedirs(app_dir, exist_ok=True)

    try:
        os.chown(app_dir, uid, gid)
    except PermissionError:
        pass

    print("✅ AppImageLauncher安装完成")
    return True

def uninstall():
    print("🔄 开始卸载AppImageLauncher...")

    current_pid = os.getpid()
    current_ppid = os.getppid()

    result = run_command_sync("pgrep -f 'appimagelauncher.*daemon'", "查找相关进程", capture_output=True, silent=True)
    if result.returncode == 0:
        for pid in result.stdout.strip().split('\n'):
            if pid and pid.strip():
                try:
                    pid_num = int(pid.strip())
                    if pid_num not in [current_pid, current_ppid]:
                        run_command_sync(f"kill {pid_num}", f"停止进程 {pid_num}", capture_output=True)
                except ValueError:
                    continue

    run_command_sync("sudo snap remove appimagelauncher", "检查并卸载Snap版本", capture_output=True)
    run_command_sync("sudo apt remove -y appimagelauncher", "卸载APT包", capture_output=True)
    run_command_sync("sudo apt purge -y appimagelauncher", "清理包配置", capture_output=True)
    run_command_sync("sudo apt autoremove -y", "清理依赖包", capture_output=True)

    _, user_home, _, _ = get_user_info()
    config_dirs = [
        os.path.join(user_home, ".config/appimagelauncher"),
        os.path.join(user_home, ".local/share/appimagelauncher"),
        os.path.join(user_home, ".cache/appimagelauncher")
    ]

    for config_dir in config_dirs:
        if os.path.exists(config_dir):
            run_command_sync(f"rm -rf '{config_dir}'", f"清理配置目录 {os.path.basename(config_dir)}", capture_output=True)

    print("✅ 卸载完成")
    return True

def check_status():
    result = run_command_sync("dpkg -l appimagelauncher", capture_output=True, silent=True)
    if result.returncode == 0 and "ii" in result.stdout:
        print('🟢 状态: 已安装')
        return 0

    result = run_command_sync("snap list appimagelauncher", capture_output=True, silent=True)
    if result.returncode == 0:
        print('🟢 状态: 已安装')
        return 0

    print('🔴 状态: 未安装')
    return 2

if __name__ == "__main__":
    import sys

    command = sys.argv[1] if len(sys.argv) > 1 else "install"

    if command == "uninstall" or command == "--uninstall":
        success = uninstall()
        sys.exit(0 if success else 1)
    elif command == "status":
        exit_code = check_status()
        sys.exit(exit_code)
    else:
        success = install()
        sys.exit(0 if success else 1)
