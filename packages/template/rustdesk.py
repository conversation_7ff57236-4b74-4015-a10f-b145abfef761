#!/usr/bin/env python3
"""
RustDesk 远程桌面
支持平台: Ubuntu/Debian
架构: x86_64
"""

import os
import sys
import subprocess
import requests
import shutil
import time
import socket

current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
sys.path.insert(0, project_root)
from packages.common import get_user_info, run_command_sync

from packages.common.package_downloader import download_package

def get_local_ip():
    """获取本地IP地址"""
    try:
        # 创建一个UDP socket连接到外部地址来获取本地IP
        with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as s:
            # 连接到一个外部地址（不会实际发送数据）
            s.connect(("*******", 80))
            local_ip = s.getsockname()[0]
            return local_ip
    except Exception:
        try:
            # 备用方法：获取hostname对应的IP
            return socket.gethostbyname(socket.gethostname())
        except Exception:
            # 最后的备用方案
            return "127.0.0.1"
def apply_rustdesk_config():
    """应用RustDesk配置 - 按照验证成功的步骤"""
    _, user_home, uid, gid = get_user_info()
    config_dir = os.path.join(user_home, ".config/rustdesk")
    os.makedirs(config_dir, exist_ok=True)

    # 网络配置参数
    local_ip = get_local_ip()
    network_config = {
        "id_server": "************",
        "relay_server": "************",
        "key": "qwqmX039upUiBz8vy+rpBSdhKahYePomV6y5ZYUofQA=",
        "password": "Hmx@525214",
        "local_ip": local_ip
    }

    print(f"🔧 配置RustDesk服务器: {network_config['id_server']}")
    print(f"🔧 本地IP地址: {network_config['local_ip']}")
    print("🔧 开始配置过程...")

    # 关键步骤1：精确停止RustDesk进程（避免杀死安装脚本自己）
    print("🔧 停止RustDesk进程...")
    try:
        # 只杀死RustDesk可执行文件进程，不杀死包含rustdesk字符串的其他进程
        run_command_sync("sudo pkill -f '/usr/bin/rustdesk'", "停止RustDesk主进程", capture_output=True, silent=True)
        run_command_sync("sudo pkill -x rustdesk", "停止RustDesk进程", capture_output=True, silent=True)
        # 停止systemd服务
        run_command_sync("sudo systemctl stop rustdesk", "停止RustDesk服务", capture_output=True, silent=True)
        time.sleep(3)  # 等待进程完全停止
        print("✅ RustDesk进程已停止")
    except:
        print("⚠️ 停止进程时出现异常，继续配置")

    # 关键步骤2：在进程停止状态下写入完整配置文件
    print("🔧 写入RustDesk配置文件...")
    config_content = f"""rendezvous_server = '{network_config['id_server']}'
nat_type = 2
serial = 0
unlock_pin = ''
trusted_devices = ''

[options]
custom-rendezvous-server = '{network_config['id_server']}'
relay-server = '{network_config['relay_server']}'
key = '{network_config['key']}'
allow-ip-access = 'Y'
allow_ip_access = 'Y'
direct-server = 'Y'
access-mode = 'full'
verification-method = 'use-permanent-password'
local-ip-addr = '{network_config['local_ip']}'
av1-test = 'Y'
enable-file-transfer = 'Y'
enable-clipboard = 'Y'
enable-audio = 'Y'
enable-tunnel = 'Y'
enable-remote-restart = 'Y'
auto-accept-incoming = 'Y'
allow-auto-accept = 'Y'
unattended-access = 'Y'
enable-keyboard = 'Y'
enable-remote-config = 'Y'
approve-mode = '0'
auto-disconnect-timeout = '0'
whitelist = ''
enable-confirm-closing-tabs = 'N'
enable-confirm-closing-tabs-on-exit = 'N'
enable-confirm-closing-tabs-on-exit-with-multiple-tabs = 'N'
"""

    # 写入配置文件
    config_file = os.path.join(config_dir, "RustDesk2.toml")
    with open(config_file, 'w') as f:
        f.write(config_content)

    # 关键步骤：设置文件权限（按照验证的成功步骤）
    os.chmod(config_file, 0o600)
    print("🔧 设置配置文件权限...")

    try:
        os.chown(config_file, uid, gid)
        os.chown(config_dir, uid, gid)
        print("✅ 配置文件权限设置完成")
    except PermissionError:
        print("⚠️ 权限设置失败，但继续配置")

    print("✅ 配置文件写入完成")

    # 关键步骤3：启动服务
    print("🔧 启动RustDesk服务...")
    run_command_sync("sudo systemctl start rustdesk", "启动RustDesk服务", capture_output=True)

    # 直接启动RustDesk GUI
    try:
        print("🔧 启动RustDesk GUI...")
        subprocess.Popen(['rustdesk'], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
        time.sleep(15)  # 等待GUI启动
    except Exception as e:
        print(f"⚠️ GUI启动失败: {e}")

    # 关键步骤4：使用sudo设置密码（这是关键）
    print("🔧 设置固定密码...")
    try:
        result = subprocess.run(
            f"sudo rustdesk --password '{network_config['password']}'",
            shell=True,
            check=False,
            capture_output=True,
            text=True,
            timeout=15
        )
        if result.returncode == 0:
            print("✅ 密码设置成功")
        else:
            print("⚠️ 密码设置失败，请手动设置")
            print(f"   错误信息: {result.stderr.strip() if result.stderr else '无错误信息'}")
    except subprocess.TimeoutExpired:
        print("⚠️ 密码设置超时")
    except Exception as e:
        print(f"⚠️ 密码设置异常: {e}")

    print("✅ RustDesk配置完成")
    print("� 配置信息:")
    print(f"   🖥️  服务器: {network_config['id_server']}")
    print(f"   🔑 密码: {network_config['password']}")
    print("   📱 支持无人值守访问")

def get_latest_version():
    try:
        response = requests.get(
            "https://api.github.com/repos/rustdesk/rustdesk/releases/latest",
            timeout=10
        )
        if response.status_code == 200:
            data = response.json()
            return data.get('tag_name', '').lstrip('v')
    except:
        pass
    return "1.4.0"

def download(temp_dir, download_now=True):
    version = get_latest_version()
    if not download_now:
        return version, None

    filename = f"rustdesk-{version}-x86_64.deb"
    local_path = os.path.join(temp_dir, filename)

    try:
        print(f"📥 下载RustDesk...")
        download_url = f"https://github.com/rustdesk/rustdesk/releases/download/{version}/{filename}"

        response = requests.get(download_url, stream=True, timeout=60)
        response.raise_for_status()

        total_size = int(response.headers.get('content-length', 0))
        downloaded = 0

        with open(local_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)
                    downloaded += len(chunk)
                    if total_size > 0:
                        progress = (downloaded / total_size) * 100
                        print(f"\r📊 下载进度: {progress:.1f}%", end='', flush=True)

        print()

        if os.path.exists(local_path) and os.path.getsize(local_path) > 1024 * 1024:
            print(f"✅ 下载成功")
            return version, local_path
        else:
            print(f"❌ 下载的文件大小异常")
            if os.path.exists(local_path):
                os.remove(local_path)
            return version, None

    except Exception as e:
        print(f"❌ 下载失败: {e}")
        if os.path.exists(local_path):
            os.remove(local_path)
        return version, None

def install():
    """安装RustDesk - 按照验证成功的步骤"""
    status_code = check_status()
    if status_code != 2:
        uninstall()

    # 第一阶段：下载和安装
    downloaded_file, _ = download_package(
        package_name="rustdesk",
        online_downloader=download,
        package_type="generic"
    )

    if not downloaded_file or not os.path.exists(downloaded_file):
        print("❌ 下载失败")
        return False

    print("🔧 开始安装RustDesk...")

    # 关键步骤1：安装deb包
    run_command_sync(f"sudo dpkg -i '{downloaded_file}'", "安装RustDesk包", capture_output=True)

    # 关键步骤2：修复依赖
    run_command_sync("sudo apt install -f -y", "修复安装依赖", capture_output=True)

    print("✅ RustDesk安装完成")

    # 第二阶段：配置
    apply_rustdesk_config()

    print("✅ RustDesk远程桌面安装和配置完成")
    return True

def uninstall():
    print("🔄 开始卸载RustDesk远程桌面...")

    # 停止服务
    run_command_sync("sudo systemctl stop rustdesk", "停止RustDesk服务", capture_output=True)
    run_command_sync("sudo systemctl disable rustdesk", "禁用RustDesk服务", capture_output=True)

    # 卸载软件包
    run_command_sync("sudo snap remove rustdesk", "检查并卸载Snap版本", capture_output=True)
    run_command_sync("sudo apt-get purge -y rustdesk", "卸载软件包", capture_output=True)
    run_command_sync("sudo apt-get autoremove -y", "清理依赖包", capture_output=True)

    # 清理系统文件
    run_command_sync("sudo rm -f /usr/bin/rustdesk", "清理可执行文件", capture_output=True)
    run_command_sync("sudo rm -f /usr/share/applications/rustdesk.desktop", "清理桌面文件", capture_output=True)
    run_command_sync("sudo rm -rf /opt/rustdesk", "清理安装目录", capture_output=True)

    # 清理用户配置
    _, user_home, _, _ = get_user_info()
    config_paths = [
        os.path.join(user_home, ".config/rustdesk"),
        os.path.join(user_home, ".cache/rustdesk"),
        os.path.join(user_home, ".local/share/rustdesk"),
        os.path.join(user_home, ".config/autostart/rustdesk.desktop")
    ]

    for config_path in config_paths:
        if os.path.exists(config_path):
            run_command_sync(f"rm -rf '{config_path}'", f"清理 {os.path.basename(config_path)}", capture_output=True)

    # 最后停止可能残留的进程
    run_command_sync("sudo pkill -f '/usr/bin/rustdesk' || true", "清理残留进程", capture_output=True)

    print("✅ 卸载完成")
    return True

def check_status():
    if shutil.which('rustdesk'):
        print('🟢 状态: 已安装')
        return 0

    result = run_command_sync("snap list rustdesk", capture_output=True, silent=True)
    if result.returncode == 0:
        print('🟢 状态: 已安装')
        return 0

    print('🔴 状态: 未安装')
    return 2

if __name__ == "__main__":
    import sys

    command = sys.argv[1] if len(sys.argv) > 1 else "install"

    if command == "uninstall" or command == "--uninstall":
        success = uninstall()
        sys.exit(0 if success else 1)
    elif command == "status":
        exit_code = check_status()
        sys.exit(exit_code)
    else:
        success = install()
        sys.exit(0 if success else 1)
