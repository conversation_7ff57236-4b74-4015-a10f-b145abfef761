#!/usr/bin/env python3
"""
X11显示服务器切换
支持平台: Ubuntu/Debian
架构: 通用
"""

import os
import sys

current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
sys.path.insert(0, project_root)
from packages.common import run_command_sync, get_user_info

def install():
    status_code = check_status()
    if status_code == 0:
        print("✅ 系统已配置为X11显示服务器")
        return True

    run_command_sync("sudo apt-get update", "更新包列表", capture_output=True)

    x11_packages = "xorg xserver-xorg xserver-xorg-core xwayland"
    run_command_sync(f"sudo apt-get install -y {x11_packages}", "安装X11依赖")

    # 获取当前用户名用于自动登录
    current_user, _, _, _ = get_user_info()

    # 配置GDM禁用Wayland，强制使用X11，并启用自动登录
    gdm_config_content = f"""# GDM configuration storage
#
# See /usr/share/gdm/gdm.schemas for a list of available options.

[daemon]
# Uncomment the line below to force the login screen to use Xorg
WaylandEnable=false
# Enable automatic login
AutomaticLoginEnable=true
AutomaticLogin={current_user}

[security]

[xdmcp]

[chooser]

[debug]
# Uncomment the line below to turn on debugging
# More verbose logs
# Additionally lets the X server dump core if it crashes
#Enable=true
"""

    with open("/tmp/gdm_custom.conf", "w") as f:
        f.write(gdm_config_content)

    run_command_sync("sudo cp /tmp/gdm_custom.conf /etc/gdm3/custom.conf", "配置GDM禁用Wayland")
    run_command_sync("rm -f /tmp/gdm_custom.conf", capture_output=True)

    # 重启GDM服务使配置生效
    run_command_sync("sudo systemctl restart gdm3", "重启GDM服务")

    print("✅ 系统X11配置完成")
    print("📝 系统已配置为强制使用X11显示服务器")
    print(f"🔐 已启用自动登录用户: {current_user}")
    return True

def uninstall():
    print("🔄 开始恢复系统默认Wayland配置...")

    # 恢复GDM默认配置，重新启用Wayland，禁用自动登录
    gdm_config_content = """# GDM configuration storage
#
# See /usr/share/gdm/gdm.schemas for a list of available options.

[daemon]
# Uncomment the line below to force the login screen to use Xorg
#WaylandEnable=false
# Disable automatic login
#AutomaticLoginEnable=false
#AutomaticLogin=

[security]

[xdmcp]

[chooser]

[debug]
# Uncomment the line below to turn on debugging
# More verbose logs
# Additionally lets the X server dump core if it crashes
#Enable=true
"""

    with open("/tmp/gdm_custom.conf", "w") as f:
        f.write(gdm_config_content)

    run_command_sync("sudo cp /tmp/gdm_custom.conf /etc/gdm3/custom.conf", "恢复GDM默认配置")
    run_command_sync("rm -f /tmp/gdm_custom.conf", capture_output=True)

    # 重启GDM服务使配置生效
    run_command_sync("sudo systemctl restart gdm3", "重启GDM服务")

    print("✅ 系统已恢复默认Wayland配置")
    print("📝 系统将使用默认Wayland显示服务器")
    print("🔐 已禁用自动登录")
    return True

def check_status():
    gdm_custom_conf = "/etc/gdm3/custom.conf"
    wayland_disabled = False

    if os.path.exists(gdm_custom_conf):
        try:
            with open(gdm_custom_conf, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line == 'WaylandEnable=false':
                        wayland_disabled = True
                        break
        except:
            pass

    if wayland_disabled:
        print('🟢 状态: 已安装')
        return 0
    else:
        print('🔴 状态: 未安装')
        return 2

if __name__ == "__main__":
    import sys

    command = sys.argv[1] if len(sys.argv) > 1 else "install"

    if command == "uninstall" or command == "--uninstall":
        success = uninstall()
        sys.exit(0 if success else 1)
    elif command == "status":
        exit_code = check_status()
        sys.exit(exit_code)
    else:
        success = install()
        sys.exit(0 if success else 1)
