#!/usr/bin/env python3
"""
VMware Workstation
支持平台: Ubuntu/Debian
架构: x86_64
"""

import os
import sys
import subprocess
import requests
import shutil
import re
import tempfile
import urllib3
import platform
import xml.etree.ElementTree as ET
from datetime import datetime
from urllib.parse import unquote
from typing import Optional, Tuple, Dict

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
sys.path.insert(0, project_root)
from packages.common import get_user_info

from packages.common.package_downloader import download_package

class NextcloudClient:
    def __init__(self, base_url, username, password):
        self.webdav_url = f"{base_url.rstrip('/')}/remote.php/webdav"
        self.auth = (username, password)

    def download_file(self, remote_path, local_path):
        remote_url = f"{self.webdav_url}/{remote_path.lstrip('/')}"
        response = requests.get(remote_url, auth=self.auth, verify=False)

        if response.status_code == 200:
            os.makedirs(os.path.dirname(local_path), exist_ok=True)
            with open(local_path, 'wb') as f:
                f.write(response.content)
            return True
        else:
            raise Exception(f"下载失败: HTTP {response.status_code}")

    def list_directory(self, remote_path="/"):
        headers = {'Depth': '1'}
        response = requests.request(
            'PROPFIND',
            f"{self.webdav_url}/{remote_path.lstrip('/')}",
            auth=self.auth,
            headers=headers,
            verify=False
        )

        if response.status_code == 207:
            root = ET.fromstring(response.content)
            ns = {'d': 'DAV:'}
            items = []

            for response_tag in root.findall('.//d:response', ns):
                href = response_tag.find('d:href', ns).text
                decoded_href = unquote(href)
                name = os.path.basename(decoded_href.rstrip('/'))
                if not name and decoded_href != '/remote.php/webdav/':
                    name = os.path.basename(decoded_href[:-1])

                propstat = response_tag.find('.//d:propstat/d:prop', ns)
                resource_type = propstat.find('d:resourcetype', ns)
                is_dir = resource_type.find('d:collection', ns) is not None
                size = propstat.find('d:getcontentlength', ns)
                size = int(size.text) if size is not None and size.text else 0
                modified = propstat.find('d:getlastmodified', ns)
                modified = datetime.strptime(modified.text, '%a, %d %b %Y %H:%M:%S %Z') if modified is not None else None

                if decoded_href != '/remote.php/webdav/':
                    items.append({
                        'name': name,
                        'path': decoded_href.replace('/remote.php/webdav/', ''),
                        'is_dir': is_dir,
                        'size': size,
                        'modified': modified.strftime('%Y-%m-%d %H:%M:%S') if modified else None
                    })
            return items
        else:
            raise Exception(f"获取目录列表失败: HTTP {response.status_code}")

    def get_latest_version(self, software_name: str, version_pattern, software_path: str = "/sanyitec/软件"):
        try:
            files = self.list_directory(software_path)
            system = platform.system().lower()
            machine = platform.machine().lower()

            latest_version = None
            latest_build = None
            latest_file = None

            for item in files:
                if not item['is_dir'] and software_name in item['name']:
                    file_name = item['name'].lower()

                    # 检查系统兼容性
                    if system == 'linux' and ('.bundle' in file_name or 'linux' in file_name):
                        if 'x86_64' in file_name and machine in ['x86_64', 'amd64']:
                            match = version_pattern.match(item['name'])
                            if match:
                                version = match.group(1)
                                build = match.group(2) if len(match.groups()) > 1 else '0'

                                if latest_version is None or (
                                    [int(p) for p in version.split('.')] > [int(p) for p in latest_version.split('.')] or
                                    (version == latest_version and int(build) > int(latest_build))
                                ):
                                    latest_version = version
                                    latest_build = build
                                    latest_file = item['name']

            if latest_version and latest_file:
                file_path = f"{software_path}/{latest_file}"
                return latest_version, latest_file, file_path
            return None, None, None
        except Exception as e:
            raise Exception(f"获取{software_name}版本信息时出错: {e}")

    def download_software(self, remote_path: str, local_path: str, min_size: int = 1000000) -> bool:
        try:
            self.download_file(remote_path, local_path)
            if os.path.exists(local_path) and os.path.getsize(local_path) > min_size:
                return True
            else:
                raise Exception("下载的文件不完整")
        except Exception as e:
            raise Exception(f"下载文件失败: {e}")
def get_latest_version():
    try:
        client = NextcloudClient("https://pan.sanyitec.cc", "sanyitec", "Sanyitec@123456")
        version_pattern = re.compile(r'VMware-Workstation-Full-(\d+\.\d+\.\d+)-(\d+)\.x86_64\.bundle')
        version, _, _ = client.get_latest_version("VMware-Workstation-Full-", version_pattern)
        if version:
            return version
    except:
        pass
    return "17.6.3"

def download(temp_dir, download_now=True):
    version = get_latest_version()
    if not download_now:
        return version, None

    try:
        client = NextcloudClient("https://pan.sanyitec.cc", "sanyitec", "Sanyitec@123456")
        version_pattern = re.compile(r'VMware-Workstation-Full-(\d+\.\d+\.\d+)-(\d+)\.x86_64\.bundle')
        version, filename, remote_path = client.get_latest_version("VMware-Workstation-Full-", version_pattern)

        if not filename:
            filename = f"VMware-Workstation-Full-{version}-24583834.x86_64.bundle"
            remote_path = f"/sanyitec/软件/{filename}"

        local_path = os.path.join(temp_dir, filename)
        print(f"📥 下载VMware Workstation...")

        client.download_software(remote_path, local_path, min_size=100 * 1024 * 1024)

        if os.path.exists(local_path) and os.path.getsize(local_path) > 100 * 1024 * 1024:
            print(f"✅ 下载成功")
            return version, local_path
        else:
            print(f"❌ 下载的文件大小异常")
            if os.path.exists(local_path):
                os.remove(local_path)
            return version, None

    except Exception as e:
        print(f"❌ 下载失败: {e}")
        return version, None

def install():
    status_code = check_status()
    if status_code != 2:
        uninstall()

    downloaded_file, _ = download_package(
        package_name="vmware",
        online_downloader=download,
        package_type="generic"
    )

    if not downloaded_file or not os.path.exists(downloaded_file):
        print("❌ 下载失败")
        return False

    print("🔄 安装依赖...")
    subprocess.run(["sudo", "add-apt-repository", "-y", "ppa:ubuntu-toolchain-r/ppa"], check=False)
    subprocess.run(["sudo", "apt", "update"], check=False)
    subprocess.run(["sudo", "apt", "install", "-y", "gcc-12", "g++-12", "gcc-12-base", "gcc-12-multilib", "cpp-12"], check=False)
    subprocess.run(["sudo", "ln", "-sf", "/usr/bin/gcc-12", "/usr/bin/gcc"], check=False)
    subprocess.run(["sudo", "ln", "-sf", "/usr/bin/g++-12", "/usr/bin/g++"], check=False)
    subprocess.run(["sudo", "apt", "install", "-y", "build-essential", "linux-headers-generic", "make", "perl"], check=False)

    os.chmod(downloaded_file, 0o755)

    print("🔄 安装VMware Workstation...")
    print(f"   安装文件: {downloaded_file}")
    print(f"   文件大小: {os.path.getsize(downloaded_file) / (1024*1024):.1f} MB")

    try:
        print("   尝试静默安装...")
        subprocess.run(
            ["sudo", downloaded_file, "--console", "--required", "--eulas-agreed"],
            check=True, capture_output=True, text=True
        )
        print("   静默安装成功")
    except subprocess.CalledProcessError as e:
        print(f"   静默安装失败: {e}")
        print("   尝试交互式安装...")
        try:
            subprocess.run(
                ["sudo", downloaded_file],
                check=True, capture_output=True, text=True
            )
            print("   交互式安装成功")
        except subprocess.CalledProcessError as e2:
            print(f"❌ 安装失败: {e2}")
            if e2.stderr:
                print(f"   错误信息: {e2.stderr}")
            return False

    print("🔄 配置服务...")

    # 对于VMware，服务是动态生成的，不需要手动启用
    # 只需要确认服务可以正常启动
    start_result = subprocess.run(["sudo", "service", "vmware", "start"],
                                 capture_output=True, text=True, check=False)

    if start_result.returncode == 0:
        print("   ✅ VMware服务启动成功")

        # 检查服务状态
        status_result = subprocess.run(["sudo", "service", "vmware", "status"],
                                     capture_output=True, text=True, check=False)
        if status_result.returncode == 0:
            print("   ✅ VMware服务运行正常")

        # VMware的服务是动态生成的，不需要手动enable
        # 它们会在需要时自动启动
        print("   ℹ️  VMware服务已配置为按需启动")

    else:
        print(f"   ⚠️  VMware服务启动失败: {start_result.stderr.strip()}")
        print("   ℹ️  这可能是正常的，VMware服务会在首次使用时启动")

    # 检查VMware配置是否需要运行
    if os.path.exists("/usr/bin/vmware-config.pl"):
        print("   ℹ️  检测到VMware配置脚本，可能需要运行配置")
        print("   💡 如果首次启动VMware时遇到问题，请运行: sudo vmware-config.pl")

    # 检查其他VMware组件服务（如果存在且需要）
    optional_services = ["vmware-networks", "vmware-usbarbitrator"]
    for service in optional_services:
        # 检查服务是否存在
        check_result = subprocess.run(
            ["systemctl", "list-unit-files", f"{service}.service"],
            capture_output=True, text=True
        )

        if service in check_result.stdout:
            print(f"   ℹ️  发现可选服务: {service}")
            # 对于可选服务，只尝试启动，不强制启用
            subprocess.run(["sudo", "systemctl", "start", service], check=False)

    print("✅ VMware Workstation安装完成")
    return True

def uninstall():
    print("🔄 开始卸载VMware Workstation...")

    print("🔄 停止VMware进程...")
    current_pid = os.getpid()
    current_ppid = os.getppid()

    result = subprocess.run("pgrep -f 'vmware.*workstation|vmware.*player'", shell=True, capture_output=True, text=True)
    if result.returncode == 0:
        for pid in result.stdout.strip().split('\n'):
            if pid and pid.strip():
                try:
                    pid_int = int(pid.strip())
                    if pid_int not in [current_pid, current_ppid]:
                        subprocess.run(f"kill {pid_int} 2>/dev/null || true", shell=True, check=False)
                except ValueError:
                    pass

    print("🔄 停止服务...")
    # 检查并停止实际存在的VMware服务
    vmware_services = [
        "vmware",
        "vmware-workstation-server",
        "vmware-networks",
        "vmware-usbarbitrator",
        "vmware-hostd"
    ]

    for service in vmware_services:
        # 检查服务是否存在
        check_result = subprocess.run(
            ["systemctl", "list-unit-files", f"{service}.service"],
            capture_output=True, text=True
        )

        if service in check_result.stdout:
            # 服务存在，尝试停止和禁用
            subprocess.run(["sudo", "systemctl", "stop", service], check=False)
            subprocess.run(["sudo", "systemctl", "disable", service], check=False)
        else:
            # 对于vmware服务，可能是SysV服务
            if service == "vmware":
                subprocess.run(["sudo", "service", "vmware", "stop"], check=False)
                subprocess.run(["sudo", "update-rc.d", "vmware", "disable"], check=False)

    print("🔄 检查并卸载Snap版本...")
    subprocess.run("sudo snap remove vmware-workstation 2>/dev/null || true", shell=True, check=False)

    print("🔄 运行官方卸载程序...")
    uninstaller_found = False

    if os.path.exists("/usr/bin/vmware-installer"):
        try:
            result = subprocess.run("sudo /usr/bin/vmware-installer --uninstall-product vmware-workstation --console --required",
                                  shell=True, check=False, timeout=20, capture_output=True)
            uninstaller_found = True
            print("   官方卸载程序执行完成")
        except subprocess.TimeoutExpired:
            print("   官方卸载程序超时，强制终止...")
            subprocess.run("sudo pkill -f vmware-installer", shell=True, check=False)
        except:
            pass

    if not uninstaller_found and os.path.exists("/usr/lib/vmware-installer/2.1.0/vmware-installer"):
        try:
            result = subprocess.run("sudo /usr/lib/vmware-installer/2.1.0/vmware-installer --uninstall-product vmware-workstation --console --required",
                                  shell=True, check=False, timeout=20, capture_output=True)
            print("   备用卸载程序执行完成")
        except subprocess.TimeoutExpired:
            print("   备用卸载程序超时，强制终止...")
            subprocess.run("sudo pkill -f vmware-installer", shell=True, check=False)
        except:
            pass

    if not uninstaller_found:
        print("   未找到官方卸载程序，直接进行手动清理")

    print("🔄 清理系统文件...")
    system_paths = [
        "/opt/vmware", "/usr/lib/vmware", "/etc/vmware", "/usr/share/vmware",
        "/var/lib/vmware", "/usr/bin/vmware*", "/usr/share/applications/vmware*.desktop"
    ]
    for path in system_paths:
        subprocess.run(f"sudo rm -rf '{path}' 2>/dev/null || true", shell=True, check=False)

    print("🔄 清理用户配置...")
    _, user_home, _, _ = get_user_info()
    user_paths = [
        os.path.join(user_home, ".vmware"),
        os.path.join(user_home, "vmware"),
        os.path.join(user_home, ".config/vmware")
    ]
    for path in user_paths:
        subprocess.run(f"rm -rf '{path}' 2>/dev/null || true", shell=True, check=False)

    subprocess.run("sudo apt-get autoremove -y", shell=True, check=False)

    print("✅ 卸载完成")
    return True

def check_status():
    vmware_paths = [
        '/usr/bin/vmware',
        '/opt/vmware/bin/vmware',
        '/usr/local/bin/vmware'
    ]

    for path in vmware_paths:
        if os.path.exists(path):
            print('🟢 状态: 已安装')
            return 0

    if shutil.which('vmware'):
        print('🟢 状态: 已安装')
        return 0

    result = subprocess.run(["snap", "list", "vmware-workstation"], capture_output=True, text=True)
    if result.returncode == 0:
        print('🟢 状态: 已安装')
        return 0

    print('🔴 状态: 未安装')
    return 2

if __name__ == "__main__":
    import sys

    command = sys.argv[1] if len(sys.argv) > 1 else "install"

    if command == "uninstall" or command == "--uninstall":
        success = uninstall()
        sys.exit(0 if success else 1)
    elif command == "status":
        exit_code = check_status()
        sys.exit(exit_code)
    else:
        success = install()
        sys.exit(0 if success else 1)
