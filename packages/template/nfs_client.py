#!/usr/bin/env python3
"""
NFS 客户端工具
支持平台: Ubuntu/Debian
架构: 通用
"""

import os
import sys
import shutil

current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
sys.path.insert(0, project_root)
from packages.common import get_user_info, run_command_sync, wait_for_apt, is_package_installed

def get_user_config():
    """获取用户配置，回车使用默认值"""
    print("🔧 NFS客户端挂载配置")
    print("=" * 50)

    nfs_server = input(f"NFS服务器IP [*************]: ").strip() or "*************"
    nfs_path = input(f"NFS服务器共享路径 [/mnt/16T]: ").strip() or "/mnt/16T"
    local_mount = input(f"本地挂载点 [/mnt/nfs]: ").strip() or "/mnt/nfs"
    bookmark_name = input(f"文件管理器书签名称 [NFS共享]: ").strip() or "NFS共享"

    # 确保挂载点是绝对路径
    if not local_mount.startswith('/'):
        local_mount = f"/mnt/{local_mount}"
        print(f"⚠️ 挂载点已修正为绝对路径: {local_mount}")

    print("\n📋 挂载选项:")
    print("1. 读写挂载 (rw,defaults,_netdev) (默认)")
    print("2. 只读挂载 (ro,defaults,_netdev)")
    print("3. 自定义挂载选项")
    choice = input("请选择 [1]: ").strip() or "1"

    if choice == "2":
        mount_options = "ro,defaults,_netdev"
    elif choice == "3":
        mount_options = input("请输入挂载选项 [rw,defaults,_netdev]: ").strip() or "rw,defaults,_netdev"
    else:
        mount_options = "rw,defaults,_netdev"

    print("\n✅ 配置确认:")
    print(f"NFS服务器: {nfs_server}")
    print(f"服务器路径: {nfs_path}")
    print(f"本地挂载点: {local_mount}")
    print(f"书签名称: {bookmark_name}")
    print(f"挂载选项: {mount_options}")
    print("=" * 50)

    return nfs_server, nfs_path, local_mount, bookmark_name, mount_options

def install():
    status_code = check_status()
    if status_code != 2:
        uninstall()

    nfs_server, nfs_path, local_mount, bookmark_name, mount_options = get_user_config()

    print("📥 安装NFS客户端工具...")

    username, user_home, uid, gid = get_user_info()

    # 等待apt进程完成（15秒超时，自动强制解决）
    wait_for_apt(max_wait_seconds=5)

    run_command_sync("sudo apt-get update", "更新包列表", capture_output=True)

    # 检查是否已安装nfs-common
    if not is_package_installed("nfs-common"):
        # 再次等待，确保没有其他apt进程（较短超时）
        wait_for_apt(max_wait_seconds=3)
        result = run_command_sync("sudo apt-get install -y nfs-common", "安装NFS客户端工具", capture_output=True)

        # 验证安装是否成功
        if result.returncode != 0 or not is_package_installed("nfs-common"):
            print("❌ NFS客户端工具安装失败，请手动安装：sudo apt-get install -y nfs-common")
            return False
    else:
        print("✅ NFS客户端工具已安装")

    run_command_sync(f"sudo mkdir -p {local_mount}", "创建挂载点", capture_output=True)
    run_command_sync("sudo cp /etc/fstab /etc/fstab.backup", "备份fstab文件", capture_output=True)

    # 检查是否已存在相同的NFS挂载配置（服务器:路径 + 挂载点的组合）
    fstab_check = f"{nfs_server}:{nfs_path}.*{local_mount}.*nfs"
    result = run_command_sync(f"grep -q '{fstab_check}' /etc/fstab", "检查fstab配置", capture_output=True, silent=True)
    if result.returncode != 0:
        # 进一步检查是否存在相同挂载点的其他NFS配置
        mount_point_check = f".*{local_mount}.*nfs"
        result2 = run_command_sync(f"grep -q '{mount_point_check}' /etc/fstab", "检查挂载点冲突", capture_output=True, silent=True)
        if result2.returncode == 0:
            print(f"⚠️ 警告：挂载点 {local_mount} 已被其他NFS配置使用")
            print("正在清理旧的NFS配置...")
            run_command_sync(f"sudo sed -i '\\|.*{local_mount}.*nfs|d' /etc/fstab", "清理旧的NFS配置", capture_output=True)

        fstab_line = f"{nfs_server}:{nfs_path} {local_mount} nfs {mount_options} 0 0"
        run_command_sync(f"echo '{fstab_line}' | sudo tee -a /etc/fstab", "添加NFS挂载配置", capture_output=True)
    else:
        print(f"✅ NFS配置已存在，跳过添加")

    # 尝试挂载NFS共享
    result = run_command_sync(f"sudo mount -t nfs {nfs_server}:{nfs_path} {local_mount}", "挂载NFS共享", capture_output=True)
    if result.returncode != 0:
        print("⚠️ 挂载失败，可能的原因：")
        print("  1. NFS服务器未启动或不可达")
        print("  2. 共享路径不存在或权限不足")
        print("  3. 网络连接问题")
        print(f"  手动挂载命令：sudo mount -t nfs {nfs_server}:{nfs_path} {local_mount}")
        print("  可以稍后手动执行挂载命令")

    # 检查目录权限，如果已经可写则跳过chown
    result = run_command_sync(f"test -w {local_mount}", "检查目录写权限", capture_output=True, silent=True)
    if result.returncode == 0:
        print("✅ 目录已具有写权限，跳过所有者设置")
    else:
        # 尝试设置所有者，如果失败也不影响整体安装
        result = run_command_sync(f"sudo chown {username}:{username} {local_mount}", "设置目录权限", capture_output=True)
        if result.returncode != 0:
            print("⚠️ 无法修改目录所有者（NFS服务器限制），但目录仍可正常使用")

    bookmarks_file = os.path.join(user_home, ".config/gtk-3.0/bookmarks")
    os.makedirs(os.path.dirname(bookmarks_file), exist_ok=True)

    bookmark_entry = f"file://{local_mount} {bookmark_name}\n"
    bookmark_exists = False
    if os.path.exists(bookmarks_file):
        with open(bookmarks_file, 'r', encoding='utf-8') as f:
            content = f.read()
            if local_mount in content:
                bookmark_exists = True

    if not bookmark_exists:
        with open(bookmarks_file, 'a', encoding='utf-8') as f:
            f.write(bookmark_entry)
        try:
            os.chown(bookmarks_file, uid, gid)
        except PermissionError:
            pass

    print("✅ NFS客户端工具安装完成")
    print("\n" + "=" * 50)
    print("📋 挂载信息:")
    print(f"- NFS服务器: {nfs_server}")
    print(f"- 服务器路径: {nfs_path}")
    print(f"- 本地挂载点: {local_mount}")
    print(f"- 挂载选项: {mount_options}")
    print(f"- 文件管理器书签: {bookmark_name}")
    print("\n🔧 手动操作命令:")
    print(f"- 挂载: sudo mount -t nfs {nfs_server}:{nfs_path} {local_mount}")
    print(f"- 卸载: sudo umount {local_mount}")
    print("=" * 50)
    return True

def uninstall():
    print("🔄 开始卸载NFS客户端工具...")

    _, user_home, uid, gid = get_user_info()

    # 尝试卸载所有可能的NFS挂载点
    result = run_command_sync("mount | grep nfs", capture_output=True, silent=True)
    if result.returncode == 0:
        mount_lines = result.stdout.strip().split('\n')
        for line in mount_lines:
            if 'nfs' in line:
                parts = line.split()
                if len(parts) >= 3:
                    mount_point = parts[2]
                    run_command_sync(f"sudo umount {mount_point}", f"卸载NFS共享 {mount_point}", capture_output=True)

    # 清理fstab中的NFS条目（更精确的匹配）
    run_command_sync("sudo sed -i '/.*:.*\/.*nfs.*_netdev/d' /etc/fstab", "清理fstab中的NFS配置", capture_output=True)

    run_command_sync("sudo apt-get purge -y nfs-common", "卸载NFS客户端工具", capture_output=True)
    run_command_sync("sudo apt-get autoremove -y", "清理依赖包", capture_output=True)

    # 清理文件管理器书签中的NFS相关条目
    bookmarks_file = os.path.join(user_home, ".config/gtk-3.0/bookmarks")
    if os.path.exists(bookmarks_file):
        with open(bookmarks_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()

        filtered_lines = [line for line in lines if not line.startswith("file:///mnt/nfs")]

        if len(filtered_lines) != len(lines):
            with open(bookmarks_file, 'w', encoding='utf-8') as f:
                f.writelines(filtered_lines)
            try:
                os.chown(bookmarks_file, uid, gid)
            except PermissionError:
                pass

    print("✅ 卸载完成")
    return True

def check_status():
    if shutil.which('mount.nfs'):
        print('🟢 状态: 已安装')
        return 0
    else:
        print('🔴 状态: 未安装')
        return 2

if __name__ == "__main__":
    command = sys.argv[1] if len(sys.argv) > 1 else "install"

    if command == "uninstall" or command == "--uninstall":
        success = uninstall()
        sys.exit(0 if success else 1)
    elif command == "status":
        exit_code = check_status()
        sys.exit(exit_code)
    else:
        success = install()
        sys.exit(0 if success else 1)
