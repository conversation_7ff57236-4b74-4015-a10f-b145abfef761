#!/usr/bin/env python3
"""
Google Chrome 浏览器
支持平台: Ubuntu/Debian
架构: amd64
"""

import os
import sys
import subprocess
import requests
import shutil

current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
sys.path.insert(0, project_root)
from packages.common import get_user_info

from packages.common.package_downloader import download_package

def get_latest_version():
    try:
        response = requests.get(
            "https://dl.google.com/linux/chrome/deb/dists/stable/main/binary-amd64/Packages",
            timeout=10
        )
        if response.status_code == 200:
            for line in response.text.split('\n'):
                if line.startswith('Version:'):
                    return line.split(':')[1].strip()
    except:
        pass
    return "latest"

def download(temp_dir, download_now=True):
    version = get_latest_version()
    if not download_now:
        return version, None

    filename = "google-chrome-stable_current_amd64.deb"
    local_path = os.path.join(temp_dir, filename)

    # 多个下载源，包括官方源和国内镜像源
    download_sources = [
        {
            "name": "Google官方源",
            "url": "https://dl.google.com/linux/direct/google-chrome-stable_current_amd64.deb",
            "timeout": 60
        },
        {
            "name": "清华大学镜像源",
            "url": "https://mirrors.tuna.tsinghua.edu.cn/google-chrome/pool/main/g/google-chrome-stable/google-chrome-stable_current_amd64.deb",
            "timeout": 60
        }
    ]

    for i, source in enumerate(download_sources, 1):
        try:
            print(f"📥 尝试从{source['name']}下载Chrome (方式{i}/{len(download_sources)})...")

            response = requests.get(
                source["url"],
                stream=True,
                timeout=source["timeout"],
                headers={'User-Agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36'}
            )
            response.raise_for_status()

            # 显示下载进度
            total_size = int(response.headers.get('content-length', 0))
            downloaded = 0

            with open(local_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
                        downloaded += len(chunk)
                        if total_size > 0:
                            progress = (downloaded / total_size) * 100
                            print(f"\r📊 下载进度: {progress:.1f}%", end='', flush=True)

            print()  # 换行

            # 验证文件大小
            if os.path.exists(local_path) and os.path.getsize(local_path) > 1024 * 1024:  # 至少1MB
                print(f"✅ 从{source['name']}下载成功")
                return version, local_path
            else:
                print(f"❌ 下载的文件大小异常")
                if os.path.exists(local_path):
                    os.remove(local_path)
                continue

        except Exception as e:
            print(f"❌ 从{source['name']}下载失败: {e}")
            if os.path.exists(local_path):
                os.remove(local_path)
            continue

    print("❌ 所有下载源都失败了")
    return version, None

def install():
    status_code = check_status()
    if status_code != 2:
        uninstall()

    downloaded_file, _ = download_package(
        package_name="chrome",
        online_downloader=download,
        package_type="generic"
    )

    if not downloaded_file or not os.path.exists(downloaded_file):
        print("❌ 下载失败")
        return False

    subprocess.run(["sudo", "dpkg", "-i", downloaded_file], check=True)
    subprocess.run(["sudo", "apt-get", "install", "-f", "-y"], check=True)

    print("✅ Chrome安装完成")
    return True

def uninstall():
    print("🔄 开始卸载Chrome...")

    # 1. 停止Chrome进程
    print("🔄 停止Chrome进程...")
    subprocess.run("pkill -f google-chrome", shell=True, check=False)

    # 2. 卸载Snap版本
    print("🔄 检查并卸载Snap版本...")
    subprocess.run("sudo snap remove chromium 2>/dev/null || true", shell=True, check=False)
    subprocess.run("sudo snap remove google-chrome 2>/dev/null || true", shell=True, check=False)

    # 3. 卸载APT包 - 使用更强制的方式
    print("🔄 卸载APT包...")
    # 先尝试正常卸载
    result = subprocess.run("sudo apt-get remove -y google-chrome-stable", shell=True, capture_output=True, text=True)
    if result.returncode != 0:
        print(f"⚠️ 正常卸载失败: {result.stderr}")

    # 强制清除
    print("🔄 强制清除包...")
    subprocess.run("sudo apt-get purge -y google-chrome-stable google-chrome-beta google-chrome-unstable chromium-browser", shell=True, check=False)

    # 使用dpkg强制删除
    print("🔄 使用dpkg强制删除...")
    subprocess.run("sudo dpkg --remove --force-remove-reinstreq google-chrome-stable", shell=True, check=False)
    subprocess.run("sudo dpkg --purge --force-remove-reinstreq google-chrome-stable", shell=True, check=False)

    # 4. 清理APT源和密钥
    print("🔄 清理APT源和密钥...")
    subprocess.run("sudo rm -f /etc/apt/sources.list.d/google-chrome.list", shell=True, check=False)
    subprocess.run("sudo rm -f /usr/share/keyrings/google-chrome-keyring.gpg", shell=True, check=False)

    # 5. 清理自动安装的依赖
    print("🔄 清理依赖...")
    subprocess.run("sudo apt-get autoremove -y", shell=True, check=False)
    subprocess.run("sudo apt-get autoclean", shell=True, check=False)

    # 6. 清理用户配置文件
    print("🔄 清理用户配置文件...")
    _, user_home, _, _ = get_user_info()
    subprocess.run(f"rm -rf '{user_home}/.config/google-chrome'", shell=True, check=False)
    subprocess.run(f"rm -rf '{user_home}/.cache/google-chrome'", shell=True, check=False)
    subprocess.run(f"rm -rf '{user_home}/.config/chromium'", shell=True, check=False)
    subprocess.run(f"rm -rf '{user_home}/.cache/chromium'", shell=True, check=False)
    subprocess.run(f"rm -rf '{user_home}/.local/share/applications/google-chrome.desktop'", shell=True, check=False)

    # 7. 清理系统级配置
    print("🔄 清理系统级配置...")
    subprocess.run("sudo rm -rf /opt/google", shell=True, check=False)
    subprocess.run("sudo rm -rf /usr/share/applications/google-chrome.desktop", shell=True, check=False)
    subprocess.run("sudo rm -rf /usr/share/pixmaps/google-chrome.png", shell=True, check=False)

    print("✅ 卸载完成")
    return True

def check_status():
    if shutil.which('google-chrome'):
        print('🟢 状态: 已安装')
        return 0

    result = subprocess.run(["snap", "list", "google-chrome"], capture_output=True, text=True)
    if result.returncode == 0:
        print('🟢 状态: 已安装')
        return 0

    print('🔴 状态: 未安装')
    return 2

if __name__ == "__main__":
    import sys

    command = sys.argv[1] if len(sys.argv) > 1 else "install"

    if command == "uninstall" or command == "--uninstall":
        success = uninstall()
        sys.exit(0 if success else 1)
    elif command == "status":
        exit_code = check_status()
        sys.exit(exit_code)
    else:
        success = install()
        sys.exit(0 if success else 1)