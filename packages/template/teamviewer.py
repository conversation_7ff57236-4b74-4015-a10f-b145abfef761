#!/usr/bin/env python3
"""
TeamViewer 远程桌面
支持平台: Ubuntu/Debian
架构: amd64
"""

import os
import sys
import subprocess
import requests

current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
sys.path.insert(0, project_root)
from packages.common import get_user_info, run_command_sync

from packages.common.package_downloader import download_package
def get_latest_version():
    try:
        response = requests.head(
            "https://download.teamviewer.com/download/linux/teamviewer_amd64.deb",
            timeout=10,
            allow_redirects=True
        )
        if response.status_code == 200:
            location = response.url
            if "teamviewer_" in location:
                version_part = location.split("teamviewer_")[1].split("_")[0]
                return version_part
    except:
        pass
    return "15.67.4"

def download(temp_dir, download_now=True):
    version = get_latest_version()
    if not download_now:
        return version, None

    filename = "teamviewer_amd64.deb"
    local_path = os.path.join(temp_dir, filename)

    try:
        print(f"📥 下载TeamViewer...")
        download_url = "https://download.teamviewer.com/download/linux/teamviewer_amd64.deb"

        response = requests.get(download_url, stream=True, timeout=60)
        response.raise_for_status()

        total_size = int(response.headers.get('content-length', 0))
        downloaded = 0

        with open(local_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)
                    downloaded += len(chunk)
                    if total_size > 0:
                        progress = (downloaded / total_size) * 100
                        print(f"\r📊 下载进度: {progress:.1f}%", end='', flush=True)

        print()

        if os.path.exists(local_path) and os.path.getsize(local_path) > 1024 * 1024:
            print(f"✅ 下载成功")
            return version, local_path
        else:
            print(f"❌ 下载的文件大小异常")
            if os.path.exists(local_path):
                os.remove(local_path)
            return version, None

    except Exception as e:
        print(f"❌ 下载失败: {e}")
        if os.path.exists(local_path):
            os.remove(local_path)
        return version, None

def install():
    status_code = check_status()
    if status_code != 2:
        uninstall()

    downloaded_file, _ = download_package(
        package_name="teamviewer",
        online_downloader=download,
        package_type="generic"
    )

    if not downloaded_file or not os.path.exists(downloaded_file):
        print("❌ 下载失败")
        return False

    env = os.environ.copy()
    env['DEBIAN_FRONTEND'] = 'noninteractive'

    run_command_sync("sudo fuser -ki /var/lib/dpkg/lock-frontend", "释放包管理器锁", capture_output=True)
    run_command_sync("sudo fuser -ki /var/cache/apt/archives/lock", capture_output=True)
    run_command_sync("sudo fuser -ki /var/lib/apt/lists/lock", capture_output=True)
    run_command_sync("sudo rm -f /var/lib/dpkg/lock-frontend /var/cache/apt/archives/lock /var/lib/apt/lists/lock", capture_output=True)

    run_command_sync("sudo dpkg --configure -a", "修复dpkg状态", capture_output=True)

    result = run_command_sync(f"sudo dpkg -i {downloaded_file}", "安装TeamViewer")
    if result.returncode != 0:
        run_command_sync("sudo apt-get install -f -y", "修复依赖问题", capture_output=True)
        result = run_command_sync(f"sudo dpkg -i {downloaded_file}", "重新安装TeamViewer")
        if result.returncode != 0:
            print("❌ TeamViewer安装失败")
            return False

    run_command_sync("sudo apt-get install -f -y", "修复依赖", capture_output=True)
    run_command_sync("sudo systemctl start teamviewerd", "启动TeamViewer服务", capture_output=True)
    run_command_sync("sudo systemctl enable teamviewerd", "设置开机启动", capture_output=True)

    print("✅ TeamViewer安装完成")
    return True

def uninstall():
    print("🔄 开始卸载TeamViewer...")

    current_pid = os.getpid()
    current_ppid = os.getppid()

    protected_pids = {current_pid, current_ppid}

    python_result = run_command_sync("pgrep -f 'python.*teamviewer'", capture_output=True, silent=True)
    if python_result.returncode == 0:
        for pid in python_result.stdout.strip().split('\n'):
            if pid and pid.strip():
                try:
                    protected_pids.add(int(pid.strip()))
                except ValueError:
                    continue

    result = run_command_sync("pgrep -f 'TeamViewer'", "查找TeamViewer进程", capture_output=True, silent=True)
    if result.returncode == 0:
        for pid in result.stdout.strip().split('\n'):
            if pid and pid.strip():
                try:
                    pid_num = int(pid.strip())
                    if pid_num not in protected_pids:
                        run_command_sync(f"kill {pid_num}", f"停止进程 {pid_num}", capture_output=True)
                except ValueError:
                    continue

    result = run_command_sync("pgrep -f 'teamviewerd'", "查找teamviewerd进程", capture_output=True, silent=True)
    if result.returncode == 0:
        for pid in result.stdout.strip().split('\n'):
            if pid and pid.strip():
                try:
                    pid_num = int(pid.strip())
                    if pid_num not in protected_pids:
                        run_command_sync(f"kill {pid_num}", f"停止进程 {pid_num}", capture_output=True)
                except ValueError:
                    continue

    run_command_sync("sudo systemctl stop teamviewerd", "停止TeamViewer服务", capture_output=True)
    run_command_sync("sudo systemctl disable teamviewerd", "禁用开机启动", capture_output=True)

    run_command_sync("sudo snap remove teamviewer", "检查并卸载Snap版本", capture_output=True)

    run_command_sync("sudo dpkg --configure -a", "修复dpkg状态", capture_output=True)

    run_command_sync("sudo apt-get remove --purge teamviewer -y", "卸载deb包", capture_output=True)
    run_command_sync("sudo apt-get autoremove -y", "清理依赖", capture_output=True)

    username, user_home, _, _ = get_user_info()

    config_dirs = [
        os.path.join(user_home, ".config/teamviewer"),
        os.path.join(user_home, ".cache/teamviewer"),
        os.path.join(user_home, ".local/share/teamviewer")
    ]

    for config_dir in config_dirs:
        if os.path.exists(config_dir):
            run_command_sync(f"rm -rf '{config_dir}'", f"清理配置目录 {os.path.basename(config_dir)}", capture_output=True)

    run_command_sync("sudo rm -rf /opt/teamviewer", "清理系统安装目录", capture_output=True)
    run_command_sync("sudo rm -rf /etc/teamviewer", "清理系统配置目录", capture_output=True)
    run_command_sync("sudo rm -f /usr/bin/teamviewer", "清理可执行文件", capture_output=True)
    run_command_sync("sudo rm -f /usr/share/applications/com.teamviewer.TeamViewer.desktop", "清理桌面文件", capture_output=True)

    print("✅ 卸载完成")
    return True

def check_status():
    result = run_command_sync("which teamviewer", capture_output=True, silent=True)
    if result.returncode == 0:
        result = run_command_sync("pgrep -f 'teamviewer'", capture_output=True, silent=True)
        if result.returncode == 0:
            print('🟢 状态: 已安装')
            return 0
        else:
            print('🟡 状态: 已安装')
            return 0

    result = run_command_sync("snap list teamviewer", capture_output=True, silent=True)
    if result.returncode == 0:
        print('🟢 状态: 已安装')
        return 0

    print('🔴 状态: 未安装')
    return 2

if __name__ == "__main__":
    import sys

    command = sys.argv[1] if len(sys.argv) > 1 else "install"

    if command == "uninstall" or command == "--uninstall":
        success = uninstall()
        sys.exit(0 if success else 1)
    elif command == "status":
        exit_code = check_status()
        sys.exit(exit_code)
    else:
        success = install()
        sys.exit(0 if success else 1)
