#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import subprocess
import sys

current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
sys.path.insert(0, project_root)
from packages.common import run_command_sync, wait_for_apt

def check_status():
    packages_to_check = [
        "unattended-upgrades",
        "update-notifier",
        "update-manager"
    ]

    installed_packages = 0
    for package in packages_to_check:
        result = subprocess.run(f"dpkg -l | grep -q '^ii.*{package}'",
                              shell=True, capture_output=True)
        if result.returncode == 0:
            installed_packages += 1

    if installed_packages == 0:
        print('🟢 状态: 已安装')
        return 0
    elif installed_packages <= 1:
        print('🟡 状态: 部分安装')
        return 1
    else:
        print('🔴 状态: 未安装')
        return 2

def uninstall():
    status_code = check_status()
    if status_code == 2:
        print("ℹ️ 系统自动更新仍在运行，无需启用")
        return []

    commands = [
        "sudo apt-get update",
        "sudo DEBIAN_FRONTEND=noninteractive apt-get install -y unattended-upgrades apt-listchanges",
        "echo '配置自动更新策略...'",
        "sudo cp /etc/apt/apt.conf.d/50unattended-upgrades /etc/apt/apt.conf.d/50unattended-upgrades.backup 2>/dev/null || true",
        """cat > /tmp/50unattended-upgrades << 'EOF'
// 自动更新配置
Unattended-Upgrade::Allowed-Origins {
    "${distro_id}:${distro_codename}";
    "${distro_id}:${distro_codename}-security";
    "${distro_id}:${distro_codename}-updates";
};

// 自动移除不再需要的依赖
Unattended-Upgrade::Remove-Unused-Dependencies "true";

// 自动重启（如果需要）
Unattended-Upgrade::Automatic-Reboot "false";
Unattended-Upgrade::Automatic-Reboot-Time "02:00";

// 发送邮件通知
Unattended-Upgrade::Mail "";

// 排除某些包
Unattended-Upgrade::Package-Blacklist {
//    "linux-image-generic";
//    "linux-headers-generic";
};
EOF""",
        "sudo cp /tmp/50unattended-upgrades /etc/apt/apt.conf.d/50unattended-upgrades",
        """cat > /tmp/20auto-upgrades << 'EOF'
APT::Periodic::Update-Package-Lists "1";
APT::Periodic::Unattended-Upgrade "1";
APT::Periodic::Download-Upgradeable-Packages "1";
APT::Periodic::AutocleanInterval "7";
EOF""",
        "sudo cp /tmp/20auto-upgrades /etc/apt/apt.conf.d/20auto-upgrades",
        "sudo systemctl enable unattended-upgrades",
        "sudo systemctl restart unattended-upgrades",
        "rm -f /tmp/50unattended-upgrades /tmp/20auto-upgrades",
        "echo '✅ 系统自动更新已启用！'"
    ]
    return commands

def install():
    status_code = check_status()
    if status_code == 0:
        print("ℹ️ 系统自动更新已经禁用，跳过禁用步骤")
        return True

    print("🛑 开始禁用系统自动更新...")

    wait_for_apt(max_wait_seconds=10)

    print("🔧 卸载更新通知软件包...")
    packages_to_remove = [
        "unattended-upgrades",
        "apt-listchanges",
        "update-notifier",
        "update-notifier-common",
        "update-manager",
        "update-manager-core"
    ]

    packages_str = " ".join(packages_to_remove)
    run_command_sync(f"sudo apt-get purge -y {packages_str}", "卸载更新相关软件包", capture_output=True)
    run_command_sync("sudo apt-get autoremove -y", "清理依赖包", capture_output=True)

    print("🔧 清理配置文件...")
    config_files = [
        "/etc/apt/apt.conf.d/50unattended-upgrades",
        "/etc/apt/apt.conf.d/20auto-upgrades",
        "/etc/apt/apt.conf.d/50unattended-upgrades.backup",
        "/etc/update-manager",
        "/etc/update-notifier"
    ]

    for config_file in config_files:
        run_command_sync(f"sudo rm -rf {config_file}", f"删除配置 {config_file}", capture_output=True)

    print("🔧 配置APT禁用自动更新...")
    apt_config = """// 禁用自动更新
APT::Periodic::Update-Package-Lists "0";
APT::Periodic::Unattended-Upgrade "0";
APT::Periodic::Download-Upgradeable-Packages "0";
APT::Periodic::AutocleanInterval "0";
"""

    with open('/tmp/99-disable-auto-updates', 'w') as f:
        f.write(apt_config)

    run_command_sync("sudo cp /tmp/99-disable-auto-updates /etc/apt/apt.conf.d/99-disable-auto-updates", "创建禁用配置", capture_output=True)
    run_command_sync("rm -f /tmp/99-disable-auto-updates", capture_output=True, silent=True)

    print("🔧 禁用软件源自动检查...")
    if os.path.exists("/etc/update-manager/release-upgrades"):
        run_command_sync("sudo sed -i 's/^Prompt=.*/Prompt=never/' /etc/update-manager/release-upgrades", "禁用版本升级提示", capture_output=True)
    else:
        print("   ℹ️ /etc/update-manager/release-upgrades 不存在，跳过")

    print("✅ 系统自动更新已彻底禁用并清理完成！")
    print("📋 已禁用的功能:")
    print("  - 后台自动更新 (unattended-upgrades)")
    print("  - 更新通知弹窗 (update-notifier)")
    print("  - 软件更新器 (update-manager)")
    print("  - APT定时任务 (apt-daily)")
    print("  - 版本升级提示")
    return True

if __name__ == "__main__":
    command = sys.argv[1] if len(sys.argv) > 1 else "install"

    if command == "uninstall":
        print("🔄 开始启用系统自动更新...")
        print("⚠️ 启用自动更新功能暂未实现")
    elif command == "status":
        exit_code = check_status()
        sys.exit(exit_code)
    else:
        success = install()
        sys.exit(0 if success else 1)
