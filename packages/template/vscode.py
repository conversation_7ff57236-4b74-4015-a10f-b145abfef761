#!/usr/bin/env python3
"""
Visual Studio Code 代码编辑器
支持平台: Ubuntu/Debian
架构: amd64
"""

import os
import sys
import subprocess
import requests
import shutil

current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
sys.path.insert(0, project_root)
from packages.common import get_user_info

from packages.common.package_downloader import download_package
def get_latest_version():
    try:
        response = requests.get(
            "https://api.github.com/repos/microsoft/vscode/releases/latest",
            timeout=10
        )
        if response.status_code == 200:
            data = response.json()
            return data.get('tag_name', '').lstrip('v')
    except:
        pass
    return "latest"

def download(temp_dir, download_now=True):
    version = get_latest_version()
    if not download_now:
        return version, None

    filename = "code_latest_amd64.deb"
    local_path = os.path.join(temp_dir, filename)

    try:
        print(f"📥 下载Visual Studio Code...")

        response = requests.get(
            "https://code.visualstudio.com/sha/download?build=stable&os=linux-deb-x64",
            stream=True,
            timeout=60,
            headers={'User-Agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36'}
        )
        response.raise_for_status()

        total_size = int(response.headers.get('content-length', 0))
        downloaded = 0

        with open(local_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)
                    downloaded += len(chunk)
                    if total_size > 0:
                        progress = (downloaded / total_size) * 100
                        print(f"\r📊 下载进度: {progress:.1f}%", end='', flush=True)

        print()

        if os.path.exists(local_path) and os.path.getsize(local_path) > 1024 * 1024:
            print(f"✅ 下载成功")
            return version, local_path
        else:
            print(f"❌ 下载的文件大小异常")
            if os.path.exists(local_path):
                os.remove(local_path)
            return version, None

    except Exception as e:
        print(f"❌ 下载失败: {e}")
        if os.path.exists(local_path):
            os.remove(local_path)
        return version, None

def install():
    status_code = check_status()
    if status_code != 2:
        uninstall()

    env = os.environ.copy()
    env['DEBIAN_FRONTEND'] = 'noninteractive'

    print("🔄 修复dpkg状态...")
    subprocess.run(["sudo", "dpkg", "--configure", "-a"], check=False, env=env)

    downloaded_file, _ = download_package(
        package_name="vscode",
        online_downloader=download,
        package_type="generic"
    )

    if not downloaded_file or not os.path.exists(downloaded_file):
        print("❌ 下载失败")
        return False

    print("🔄 预配置VSCode包...")
    subprocess.run(f"echo 'code code/add-microsoft-repo boolean false' | sudo debconf-set-selections", shell=True, env=env)

    subprocess.run(["sudo", "dpkg", "-i", downloaded_file], check=True, env=env)
    subprocess.run(["sudo", "apt-get", "install", "-f", "-y"], check=True, env=env)

    username, user_home, uid, gid = get_user_info()
    config_dir = os.path.join(user_home, ".config/Code/User")
    os.makedirs(config_dir, exist_ok=True)

    settings_file = os.path.join(config_dir, "settings.json")
    settings_content = """{
    "editor.formatOnSave": true,
    "editor.tabSize": 4,
    "files.trimTrailingWhitespace": true,
    "workbench.startupEditor": "none"
}"""

    with open(settings_file, 'w') as f:
        f.write(settings_content)

    try:
        os.chown(settings_file, uid, gid)
        os.chown(config_dir, uid, gid)
        os.chown(os.path.join(user_home, ".config/Code"), uid, gid)
    except PermissionError:
        pass

    print("✅ Visual Studio Code 安装完成")
    return True

def uninstall():
    print("🔄 开始卸载Visual Studio Code...")

    env = os.environ.copy()
    env['DEBIAN_FRONTEND'] = 'noninteractive'

    print("🔄 修复dpkg状态...")
    subprocess.run(["sudo", "dpkg", "--configure", "-a"], check=False, env=env)

    print("🔄 停止Visual Studio Code进程...")
    current_pid = os.getpid()
    current_ppid = os.getppid()

    result = subprocess.run("pgrep -f '/usr/share/code/code'", shell=True, capture_output=True, text=True)
    if result.returncode == 0:
        for pid in result.stdout.strip().split('\n'):
            if pid and pid.strip() and int(pid.strip()) not in [current_pid, current_ppid]:
                subprocess.run(f"kill {pid.strip()} 2>/dev/null || true", shell=True, check=False)

    subprocess.run("pkill -f 'code --no-sandbox' 2>/dev/null || true", shell=True, check=False)

    print("🔄 检查并卸载Snap版本...")
    subprocess.run("sudo snap remove code 2>/dev/null || true", shell=True, check=False)

    print("🔄 预配置VSCode包...")
    subprocess.run(f"echo 'code code/add-microsoft-repo boolean false' | sudo debconf-set-selections", shell=True, env=env)

    print("🔄 卸载APT包...")
    result = subprocess.run("sudo apt-get remove -y code", shell=True, capture_output=True, text=True, env=env)
    if result.returncode != 0:
        print(f"⚠️ 正常卸载失败: {result.stderr}")

    print("🔄 强制清除包...")
    subprocess.run("sudo apt-get purge -y code", shell=True, check=False, env=env)

    print("🔄 使用dpkg强制删除...")
    subprocess.run("sudo dpkg --remove --force-remove-reinstreq code", shell=True, check=False, env=env)
    subprocess.run("sudo dpkg --purge --force-remove-reinstreq code", shell=True, check=False, env=env)

    print("🔄 清理APT源和密钥...")
    subprocess.run("sudo rm -f /etc/apt/sources.list.d/vscode.list", shell=True, check=False)
    subprocess.run("sudo rm -f /etc/apt/trusted.gpg.d/packages.microsoft.gpg", shell=True, check=False)
    subprocess.run("sudo rm -f /usr/share/keyrings/packages.microsoft.gpg", shell=True, check=False)

    print("🔄 清理依赖...")
    subprocess.run("sudo apt-get autoremove -y", shell=True, check=False, env=env)
    subprocess.run("sudo apt-get autoclean", shell=True, check=False, env=env)

    print("🔄 清理用户配置文件...")
    _, user_home, _, _ = get_user_info()
    subprocess.run(f"rm -rf '{user_home}/.config/Code'", shell=True, check=False)
    subprocess.run(f"rm -rf '{user_home}/.vscode'", shell=True, check=False)
    subprocess.run(f"rm -rf '{user_home}/.cache/vscode*'", shell=True, check=False)
    subprocess.run(f"find '{user_home}/.cache' -name '*code*' -type d -exec rm -rf {{}} \\; 2>/dev/null || true", shell=True, check=False)
    subprocess.run(f"rm -rf '{user_home}/.local/share/applications/code.desktop'", shell=True, check=False)

    print("🔄 清理系统级配置...")
    subprocess.run("sudo rm -rf /usr/share/applications/code.desktop", shell=True, check=False)
    subprocess.run("sudo rm -rf /usr/share/pixmaps/code.png", shell=True, check=False)

    print("✅ 卸载完成")
    return True

def check_status():
    if shutil.which('code'):
        print('🟢 状态: 已安装')
        return 0

    result = subprocess.run(["snap", "list", "code"], capture_output=True, text=True)
    if result.returncode == 0:
        print('🟢 状态: 已安装')
        return 0

    print('🔴 状态: 未安装')
    return 2

if __name__ == "__main__":
    import sys

    command = sys.argv[1] if len(sys.argv) > 1 else "install"

    if command == "uninstall" or command == "--uninstall":
        success = uninstall()
        sys.exit(0 if success else 1)
    elif command == "status":
        exit_code = check_status()
        sys.exit(exit_code)
    else:
        success = install()
        sys.exit(0 if success else 1)