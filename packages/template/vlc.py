#!/usr/bin/env python3
"""
VLC 媒体播放器
支持平台: Ubuntu/Debian
架构: 通用
"""

import os
import sys
import subprocess
import shutil
import time
import stat

current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
sys.path.insert(0, project_root)
from packages.common import get_user_info

def wait_for_apt():
    """等待apt进程完成"""
    print("🔄 检查是否有其他apt进程正在运行...")

    while True:
        # 检查各种锁文件
        lock_files = [
            "/var/lib/dpkg/lock",
            "/var/lib/apt/lists/lock",
            "/var/lib/dpkg/lock-frontend"
        ]

        locked = False
        for lock_file in lock_files:
            result = subprocess.run(f"sudo fuser {lock_file} >/dev/null 2>&1", shell=True)
            if result.returncode == 0:
                locked = True
                break

        if not locked:
            break

        print("🔄 等待其他apt进程完成...")
        time.sleep(5)

    print("✅ 没有其他apt进程正在运行")

def setup_microsoft_eula():
    """预先接受Microsoft EULA"""
    print("🔄 预先接受Microsoft EULA...")

    # 等待apt进程完成
    wait_for_apt()

    # 创建临时debconf配置文件
    debconf_file = "/tmp/ttf-mscorefonts-eula.conf"
    with open(debconf_file, "w") as f:
        f.write("ttf-mscorefonts-installer msttcorefonts/accepted-mscorefonts-eula boolean true\n")
        f.write("msttcorefonts/accepted-mscorefonts-eula boolean true\n")

    # 使用debconf-set-selections设置
    subprocess.run(["sudo", "debconf-set-selections", debconf_file], check=False)

    # 检查ttf-mscorefonts-installer是否已安装
    result = subprocess.run("dpkg -l | grep -q ttf-mscorefonts-installer", shell=True)
    if result.returncode == 0:
        print("✅ ttf-mscorefonts-installer已经安装")
    else:
        print("🔄 安装ttf-mscorefonts-installer...")
        env = os.environ.copy()
        env['DEBIAN_FRONTEND'] = 'noninteractive'
        env['ACCEPT_EULA'] = 'Y'

        result = subprocess.run([
            "sudo", "apt-get", "install", "-y", "ttf-mscorefonts-installer"
        ], check=False, env=env)

        if result.returncode == 0:
            print("✅ ttf-mscorefonts-installer安装成功")
        else:
            print("⚠️ ttf-mscorefonts-installer安装失败，但仍然可以继续")

    # 清理临时文件
    subprocess.run(f"rm -f {debconf_file}", shell=True, check=False)
    print("✅ Microsoft EULA预先接受完成")

def install():
    try:
        print("🔄 开始安装VLC媒体播放器...")

        # 检查VLC实际文件是否存在
        vlc_binary = "/usr/bin/vlc"
        vlc_installed = os.path.exists(vlc_binary) and shutil.which('vlc')

        if vlc_installed:
            print("ℹ️ VLC媒体播放器已经安装")
            status_code = check_status()
            if status_code != 2:
                uninstall()
        else:
            print("🔄 检测到VLC包记录存在但文件缺失，强制重新安装...")
            # 强制卸载包记录
            subprocess.run(["sudo", "dpkg", "--remove", "--force-remove-reinstreq", "vlc"], check=False)
            subprocess.run(["sudo", "dpkg", "--purge", "--force-remove-reinstreq", "vlc"], check=False)

        print("🔄 修复包管理器状态...")
        subprocess.run(["sudo", "dpkg", "--configure", "-a"], check=False)
        subprocess.run(["sudo", "apt-get", "-f", "install"], check=False)

        print("🔄 更新软件包列表...")
        subprocess.run(["sudo", "apt-get", "update"], check=True)

        # 预先处理Microsoft EULA
        setup_microsoft_eula()

        print("🔄 安装VLC媒体播放器...")
        env = os.environ.copy()
        env['DEBIAN_FRONTEND'] = 'noninteractive'
        env['ACCEPT_EULA'] = 'Y'

        try:
            # 安装VLC完整包组
            print("🔄 安装VLC完整包组...")
            subprocess.run(["sudo", "apt-get", "install", "-y", "vlc", "vlc-bin", "vlc-data", "vlc-plugin-base"], check=True, env=env)

            # 查找VLC二进制文件位置
            print("🔄 查找VLC二进制文件位置...")
            result = subprocess.run(["dpkg", "-L", "vlc-bin"], capture_output=True, text=True)
            if result.returncode == 0:
                vlc_files = [line.strip() for line in result.stdout.split('\n') if 'vlc' in line and '/bin/' in line]
                print(f"🔍 vlc-bin包中的文件: {vlc_files}")

            # 检查多个可能的VLC位置
            vlc_paths = ["/usr/bin/vlc", "/bin/vlc", "/usr/local/bin/vlc"]
            vlc_found = False

            for vlc_path in vlc_paths:
                print(f"🔍 检查路径: {vlc_path}")
                if os.path.exists(vlc_path):
                    print(f"✅ 找到VLC二进制文件: {vlc_path}")
                    # 检查文件权限
                    file_stat = os.stat(vlc_path)
                    print(f"📋 文件权限: {oct(file_stat.st_mode)}")
                    print(f"📋 文件大小: {file_stat.st_size} bytes")
                    vlc_found = True
                    break
                else:
                    print(f"❌ 路径不存在: {vlc_path}")
                    # 检查目录是否存在
                    dir_path = os.path.dirname(vlc_path)
                    if os.path.exists(dir_path):
                        print(f"📁 目录存在: {dir_path}")
                        # 列出目录中的vlc相关文件
                        try:
                            files = [f for f in os.listdir(dir_path) if 'vlc' in f.lower()]
                            if files:
                                print(f"📋 目录中的vlc相关文件: {files}")
                                # 如果有其他vlc工具但缺少主vlc文件，说明需要重新安装
                                if 'cvlc' in files and 'vlc' not in files:
                                    print("🔍 检测到VLC工具存在但主文件缺失，需要重新安装")
                        except:
                            pass
                    else:
                        print(f"❌ 目录不存在: {dir_path}")

            if not vlc_found:
                # 尝试使用which命令查找
                result = subprocess.run(["which", "vlc"], capture_output=True, text=True)
                if result.returncode == 0 and result.stdout.strip():
                    vlc_path = result.stdout.strip()
                    print(f"✅ 通过which找到VLC: {vlc_path}")
                    vlc_found = True

            if not vlc_found:
                print("❌ 无法找到VLC二进制文件")
                # 尝试强制重新安装vlc-bin
                print("🔄 强制重新安装vlc-bin...")
                subprocess.run(["sudo", "apt-get", "install", "--reinstall", "-y", "vlc-bin"], check=True, env=env)

                # 再次检查
                if shutil.which('vlc'):
                    print("✅ 重新安装后找到VLC")
                    vlc_found = True
                else:
                    print("❌ 重新安装后仍然找不到VLC")
                    return False

            if vlc_found:
                print("✅ VLC安装验证成功")

        except subprocess.CalledProcessError as e:
            print(f"❌ 安装VLC媒体播放器时出错: {e}")
            return False

        print("🔄 安装多媒体编解码器...")
        try:
            subprocess.run([
                "sudo", "apt-get", "install", "-y", "libavcodec-extra", "libdvd-pkg"
            ], check=False, env=env)

            subprocess.run([
                "sudo", "apt-get", "install", "-y", "ubuntu-restricted-extras"
            ], check=False, env=env)
        except subprocess.CalledProcessError as e:
            print(f"⚠️ 安装编解码器时出错: {e}")

        print("🔄 配置libdvd-pkg...")
        try:
            subprocess.run(["sudo", "dpkg-reconfigure", "-f", "noninteractive", "libdvd-pkg"], check=False)
        except subprocess.CalledProcessError as e:
            print(f"⚠️ 配置libdvd-pkg时出错: {e}")

        print("🔄 配置VLC...")
        try:
            _, user_home, uid, gid = get_user_info()
            config_dir = os.path.join(user_home, ".config/vlc")
            os.makedirs(config_dir, exist_ok=True)

            config_file = os.path.join(config_dir, "vlcrc")

            # 备份现有配置
            if os.path.exists(config_file):
                backup_file = config_file + ".backup"
                shutil.copy2(config_file, backup_file)
                print(f"✅ 已备份现有VLC配置到 {backup_file}")

            config_content = """# VLC配置文件
# 启用硬件加速
avcodec-hw=vaapi
# 使用OpenGL视频输出
vout=xcb_glx
# 启用自动播放
play-and-exit=0
# 禁用崩溃处理程序
crash-handling=0
# 启用自动字幕检测
sub-autodetect-file=1
# 字幕字体大小
freetype-rel-fontsize=16
# 音频输出模块
aout=pulse
# 禁用遥测
stats=0
"""

            with open(config_file, 'w', encoding='utf-8') as f:
                f.write(config_content)

            try:
                os.chown(config_file, uid, gid)
                os.chown(config_dir, uid, gid)
            except PermissionError:
                pass

            print("✅ VLC播放器首选项配置完成")
        except Exception as e:
            print(f"⚠️ 配置VLC首选项时出错: {e}")

        print("✅ VLC媒体播放器安装成功")
        return True

    except Exception as e:
        print(f"❌ 安装VLC媒体播放器时出错: {e}")
        return False

def uninstall():
    print("🔄 开始卸载VLC媒体播放器...")

    print("🔄 停止VLC进程...")
    current_pid = os.getpid()
    current_ppid = os.getppid()

    result = subprocess.run("pgrep -f '/usr/bin/vlc'", shell=True, capture_output=True, text=True)
    if result.returncode == 0:
        for pid in result.stdout.strip().split('\n'):
            if pid and pid.strip() and int(pid.strip()) not in [current_pid, current_ppid]:
                subprocess.run(f"kill {pid.strip()} 2>/dev/null || true", shell=True, check=False)

    print("🔄 检查并卸载Snap版本...")
    subprocess.run("sudo snap remove vlc 2>/dev/null || true", shell=True, check=False)

    print("🔄 卸载VLC及相关包...")
    env = os.environ.copy()
    env['DEBIAN_FRONTEND'] = 'noninteractive'

    # 精确终止占用dpkg锁的进程
    print("🔄 检查并终止占用锁的进程...")
    current_pid = os.getpid()
    current_ppid = os.getppid()

    # 查找占用dpkg锁的具体进程
    result = subprocess.run("sudo lsof /var/lib/dpkg/lock-frontend 2>/dev/null | awk 'NR>1 {print $2}' | sort -u", shell=True, capture_output=True, text=True)
    if result.returncode == 0 and result.stdout.strip():
        for pid in result.stdout.strip().split('\n'):
            if pid and pid.strip() and int(pid.strip()) not in [current_pid, current_ppid]:
                print(f"🔄 终止占用锁的进程: {pid.strip()}")
                subprocess.run(f"sudo kill -9 {pid.strip()} 2>/dev/null || true", shell=True, check=False)

    # 查找并终止apt/dpkg相关进程（排除当前脚本）
    result = subprocess.run("pgrep -f 'apt-get|dpkg'", shell=True, capture_output=True, text=True)
    if result.returncode == 0:
        for pid in result.stdout.strip().split('\n'):
            if pid and pid.strip() and int(pid.strip()) not in [current_pid, current_ppid]:
                # 检查进程是否真的是apt/dpkg而不是脚本
                cmd_result = subprocess.run(f"ps -p {pid.strip()} -o comm= 2>/dev/null", shell=True, capture_output=True, text=True)
                if cmd_result.returncode == 0 and cmd_result.stdout.strip() in ['apt-get', 'dpkg', 'apt']:
                    print(f"🔄 终止apt/dpkg进程: {pid.strip()}")
                    subprocess.run(f"sudo kill -9 {pid.strip()} 2>/dev/null || true", shell=True, check=False)

    time.sleep(2)  # 等待进程完全终止

    # 直接删除VLC文件
    print("🔄 直接删除VLC文件...")
    subprocess.run("sudo rm -f /usr/bin/vlc || true", shell=True, check=False)
    subprocess.run("sudo rm -rf /usr/lib/vlc || true", shell=True, check=False)
    subprocess.run("sudo rm -rf /usr/share/vlc || true", shell=True, check=False)
    subprocess.run("sudo rm -f /usr/share/applications/vlc.desktop || true", shell=True, check=False)

    # 包管理器清理
    subprocess.run(["sudo", "apt-get", "remove", "-y", "vlc"], check=False, env=env)
    subprocess.run(["sudo", "apt-get", "purge", "-y", "vlc", "vlc-data"], check=False, env=env)
    subprocess.run("sudo apt-get purge -y vlc-plugin-* 2>/dev/null || true", shell=True, check=False, env=env)
    subprocess.run(["sudo", "apt-get", "autoremove", "-y"], check=False, env=env)

    print("🔄 清理用户配置文件...")
    _, user_home, _, _ = get_user_info()
    config_dir = os.path.join(user_home, ".config/vlc")
    cache_dir = os.path.join(user_home, ".cache/vlc")
    local_dir = os.path.join(user_home, ".local/share/vlc")
    subprocess.run(f"rm -rf '{config_dir}' || true", shell=True, check=False)
    subprocess.run(f"rm -rf '{cache_dir}' || true", shell=True, check=False)
    subprocess.run(f"rm -rf '{local_dir}' || true", shell=True, check=False)

    print("🔄 清理系统配置...")
    subprocess.run("sudo rm -rf /etc/vlc || true", shell=True, check=False)
    subprocess.run("sudo rm -f /usr/share/applications/vlc.desktop || true", shell=True, check=False)

    print("✅ 卸载完成")
    return True

def check_status():
    if shutil.which('vlc'):
        print('🟢 状态: 已安装')
        return 0

    result = subprocess.run(["snap", "list", "vlc"], capture_output=True, text=True)
    if result.returncode == 0:
        print('🟢 状态: 已安装')
        return 0

    print('🔴 状态: 未安装')
    return 2

if __name__ == "__main__":
    import sys

    command = sys.argv[1] if len(sys.argv) > 1 else "install"

    if command == "uninstall" or command == "--uninstall":
        success = uninstall()
        sys.exit(0 if success else 1)
    elif command == "status":
        exit_code = check_status()
        sys.exit(exit_code)
    else:
        success = install()
        sys.exit(0 if success else 1)
