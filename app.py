#!/usr/bin/env python3
"""
服务器安装管理系统
简化版本 - 支持热重载开发
"""

from flask import Flask, request, jsonify, send_from_directory
from flask_socketio import SocketIO, emit
import os
import json
import paramiko
from datetime import datetime
import threading
import time

app = Flask(__name__, static_folder='static', static_url_path='/static')
app.config['SECRET_KEY'] = 'your-secret-key-here'
socketio = SocketIO(app, cors_allowed_origins="*")

# 配置
PACKAGES_DIR = 'packages'
UPLOAD_DIR = '/tmp/server_install'

@app.route('/')
def index():
    """主页面"""
    return send_from_directory('static', 'index.html')

@app.route('/terminal')
def terminal():
    """终端页面"""
    return send_from_directory('static', 'terminal.html')

def parse_package_info(file_path):
    """解析包文件中的平台、架构和功能描述信息"""
    platform = "unknown"
    arch = "unknown"
    description = ""

    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

            # 提取文档字符串中的功能描述
            if content.startswith('#!/usr/bin/env python3\n"""') or '"""' in content:
                # 查找第一个三引号文档字符串
                start_idx = content.find('"""')
                if start_idx != -1:
                    end_idx = content.find('"""', start_idx + 3)
                    if end_idx != -1:
                        docstring = content[start_idx + 3:end_idx].strip()
                        lines = docstring.split('\n')
                        if lines:
                            # 第一行通常是功能描述
                            description = lines[0].strip()

            # 重新读取文件来解析平台和架构信息
            f.seek(0)
            for i, line in enumerate(f):
                if i >= 10:  # 只读前10行
                    break

                line = line.strip().lower()

                # 查找平台信息
                if '支持平台:' in line or 'platform:' in line:
                    if 'ubuntu' in line:
                        platform = "ubuntu"
                    elif 'centos' in line:
                        platform = "centos"
                    elif 'debian' in line:
                        platform = "debian"
                    elif 'windows' in line:
                        platform = "windows"

                # 查找架构信息
                if '架构:' in line or 'arch:' in line:
                    if 'x86' in line or 'x64' in line:
                        arch = "x86_64"
                    elif 'arm' in line:
                        arch = "arm"
                    elif '通用' in line or 'any' in line or 'universal' in line:
                        arch = "any"

    except Exception as e:
        print(f"解析文件 {file_path} 失败: {e}")

    return platform, arch, description

@app.route('/api/packages')
def get_packages():
    """获取包列表"""
    packages = []

    if not os.path.exists(PACKAGES_DIR):
        return jsonify(packages)

    # 扫描packages目录
    for root, _, files in os.walk(PACKAGES_DIR):
        for file in files:
            if file.endswith(('.py', '.sh')):
                rel_path = os.path.relpath(os.path.join(root, file), PACKAGES_DIR)
                full_path = os.path.join(root, file)
                name = os.path.splitext(file)[0]

                # 排除 common 目录下的文件，这些是通用工具模块，不应显示在包列表中
                path_parts = rel_path.split(os.sep)
                if path_parts[0] == 'common':
                    continue

                # 从文件内容解析平台、架构和描述信息
                platform, arch, description = parse_package_info(full_path)

                # 如果解析失败，尝试从目录结构获取
                if platform == "unknown":
                    if len(path_parts) >= 2:
                        platform = path_parts[0]

                if arch == "unknown":
                    if len(path_parts) > 2:
                        arch = path_parts[1]
                    else:
                        arch = "any"

                # 提取文件夹信息
                folder = path_parts[0] if len(path_parts) > 1 else "root"

                packages.append({
                    'name': name,
                    'file': rel_path,
                    'folder': folder,
                    'description': description,
                    'os': platform,
                    'arch': arch,
                    'type': 'python' if file.endswith('.py') else 'shell'
                })

    return jsonify(packages)

@app.route('/api/ssh/test', methods=['POST'])
def test_ssh_connection():
    """测试SSH连接 - 增强版诊断"""
    data = request.json
    host = data.get('host')
    username = data.get('username')
    password = data.get('password')

    # 详细的诊断信息
    diagnostic_info = {
        'host': host,
        'username': username,
        'steps': []
    }

    try:
        # 步骤1: 网络连通性测试
        diagnostic_info['steps'].append('开始网络连通性测试...')
        import socket
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        result = sock.connect_ex((host, 22))
        sock.close()

        if result != 0:
            diagnostic_info['steps'].append(f'❌ 网络连接失败: 无法连接到 {host}:22')
            return jsonify({
                'success': False,
                'message': f'网络连接失败: 无法连接到 {host}:22',
                'diagnostic': diagnostic_info
            })

        diagnostic_info['steps'].append('✅ 网络连接正常')

        # 步骤2: SSH连接测试
        diagnostic_info['steps'].append('开始SSH连接测试...')
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())

        # 增加更详细的连接参数
        ssh.connect(
            hostname=host,
            username=username,
            password=password,
            timeout=15,
            auth_timeout=15,
            banner_timeout=15
        )

        diagnostic_info['steps'].append('✅ SSH认证成功')

        # 步骤3: 测试命令执行
        diagnostic_info['steps'].append('测试命令执行...')
        _, stdout, stderr = ssh.exec_command('echo "连接测试成功" && whoami && pwd && date')
        output = stdout.read().decode().strip()
        error = stderr.read().decode().strip()

        diagnostic_info['steps'].append(f'✅ 命令执行成功')
        diagnostic_info['command_output'] = output
        if error:
            diagnostic_info['command_error'] = error

        # 步骤4: 测试目录权限
        diagnostic_info['steps'].append('测试目录权限...')
        upload_dir = f'/home/<USER>/server_install'
        _, stdout, stderr = ssh.exec_command(f'mkdir -p {upload_dir} && ls -la {upload_dir}')
        dir_output = stdout.read().decode().strip()
        dir_error = stderr.read().decode().strip()

        if dir_error:
            diagnostic_info['steps'].append(f'⚠️ 目录权限警告: {dir_error}')
        else:
            diagnostic_info['steps'].append('✅ 目录权限正常')

        diagnostic_info['directory_test'] = dir_output

        ssh.close()
        diagnostic_info['steps'].append('✅ 连接测试完成')

        return jsonify({
            'success': True,
            'message': '连接成功 - 所有测试通过',
            'diagnostic': diagnostic_info
        })

    except paramiko.AuthenticationException as e:
        diagnostic_info['steps'].append(f'❌ SSH认证失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': f'SSH认证失败: 用户名或密码错误',
            'diagnostic': diagnostic_info
        })
    except paramiko.SSHException as e:
        diagnostic_info['steps'].append(f'❌ SSH连接异常: {str(e)}')
        return jsonify({
            'success': False,
            'message': f'SSH连接异常: {str(e)}',
            'diagnostic': diagnostic_info
        })
    except socket.timeout as e:
        diagnostic_info['steps'].append(f'❌ 连接超时: {str(e)}')
        return jsonify({
            'success': False,
            'message': f'连接超时: 请检查网络连接和防火墙设置',
            'diagnostic': diagnostic_info
        })
    except Exception as e:
        diagnostic_info['steps'].append(f'❌ 未知错误: {str(e)}')
        return jsonify({
            'success': False,
            'message': f'连接失败: {str(e)}',
            'diagnostic': diagnostic_info
        })

@app.route('/api/ssh/clear-directory', methods=['POST'])
def clear_server_directory():
    """清除服务器目录，确保本地和服务器一致"""
    data = request.json
    host = data.get('host')
    username = data.get('username')
    password = data.get('password')

    try:
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        ssh.connect(host, username=username, password=password, timeout=10)

        upload_dir = f'/home/<USER>/server_install'

        # 清除整个目录并重新创建
        commands = [
            f'rm -rf {upload_dir}',
            f'mkdir -p {upload_dir}/packages'
        ]

        for cmd in commands:
            _, stdout, stderr = ssh.exec_command(cmd)
            stderr_output = stderr.read().decode().strip()
            if stderr_output:
                print(f"命令执行警告: {cmd} - {stderr_output}")

        ssh.close()

        return jsonify({
            'success': True,
            'message': f'服务器目录 {upload_dir} 已清除并重新创建'
        })

    except Exception as e:
        return jsonify({'success': False, 'message': f'清除目录失败: {str(e)}'})


@app.route('/api/ssh/upload', methods=['POST'])
def upload_packages():
    """上传packages目录到远程服务器 - 使用SSH命令方式"""
    data = request.json
    host = data.get('host')
    username = data.get('username')
    password = data.get('password')

    try:
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        ssh.connect(host, username=username, password=password, timeout=10)

        upload_dir = f'/home/<USER>/server_install'

        # 创建远程工作目录
        _, stdout, stderr = ssh.exec_command(f'mkdir -p {upload_dir}/packages')

        uploaded_files = []

        # 使用base64编码传输文件内容（改进版）
        def upload_file_content(local_path, remote_path):
            try:
                with open(local_path, 'rb') as f:
                    content = f.read()

                # 使用base64编码
                import base64
                encoded_content = base64.b64encode(content).decode('utf-8')

                # 创建远程目录
                remote_dir = os.path.dirname(remote_path)
                ssh.exec_command(f'mkdir -p {remote_dir}')

                # 将base64内容写入临时文件，然后解码
                temp_file = f'{remote_path}.tmp'

                # 分小块写入，避免命令行过长
                chunk_size = 4000  # 4KB chunks
                ssh.exec_command(f'> {temp_file}')  # 清空临时文件

                for i in range(0, len(encoded_content), chunk_size):
                    chunk = encoded_content[i:i + chunk_size]
                    # 使用printf避免echo的限制
                    cmd = f'printf "%s" "{chunk}" >> {temp_file}'
                    _, _, stderr = ssh.exec_command(cmd)

                    error = stderr.read().decode().strip()
                    if error:
                        print(f"写入块失败: {error}")
                        return False

                # 解码base64文件
                decode_cmd = f'base64 -d {temp_file} > {remote_path} && rm {temp_file}'
                _, _, stderr = ssh.exec_command(decode_cmd)

                error = stderr.read().decode().strip()
                if error:
                    print(f"解码失败: {error}")
                    return False

                # 设置执行权限（如果是脚本文件）
                if remote_path.endswith(('.py', '.sh')):
                    ssh.exec_command(f'chmod +x {remote_path}')

                return True
            except Exception as e:
                print(f"上传文件失败: {local_path} -> {remote_path}, 错误: {e}")
                return False

        # 上传主安装程序
        if upload_file_content('main_installer.py', f'{upload_dir}/main_installer.py'):
            uploaded_files.append('main_installer.py')

        # 上传packages目录中的所有文件（包括common目录下的通用工具模块）
        for root, _, files in os.walk(PACKAGES_DIR):
            for file in files:
                if file.endswith(('.py', '.sh')):
                    local_path = os.path.join(root, file)
                    rel_path = os.path.relpath(local_path, PACKAGES_DIR)
                    remote_path = f'{upload_dir}/packages/{rel_path}'

                    # 创建远程子目录
                    remote_dir = os.path.dirname(remote_path)
                    ssh.exec_command(f'mkdir -p {remote_dir}')

                    if upload_file_content(local_path, remote_path):
                        uploaded_files.append(f'packages/{rel_path}')

        ssh.close()

        return jsonify({
            'success': True,
            'message': f'成功上传 {len(uploaded_files)} 个文件到 {upload_dir}',
            'files': uploaded_files,
            'upload_dir': upload_dir
        })

    except Exception as e:
        return jsonify({'success': False, 'message': f'上传失败: {str(e)}'})


def fix_sftp_config(ssh, username, password):
    """修复SFTP配置 - 增强版本"""
    result = {
        'success': False,
        'message': '',
        'details': [],
        'ssh_restarted': False
    }

    try:
        print(f"正在修复SFTP配置...")
        result['details'].append("开始SFTP配置修复流程")

        # 读取本地的Python SFTP修复脚本
        python_script_path = 'packages/common/fix_sftp_config.py'
        if not os.path.exists(python_script_path):
            error_msg = f"找不到SFTP修复Python脚本文件: {python_script_path}"
            print(f"错误：{error_msg}")
            result['message'] = error_msg
            return result

        with open(python_script_path, 'r', encoding='utf-8') as f:
            python_script = f.read()

        result['details'].append("✅ 本地SFTP修复脚本读取成功")

        # 上传Python脚本到远程服务器
        remote_script_path = f'/tmp/fix_sftp_config.py'

        # 使用base64编码传输脚本
        import base64
        encoded_script = base64.b64encode(python_script.encode('utf-8')).decode('utf-8')

        # 分块写入脚本
        chunk_size = 4000
        ssh.exec_command(f'> "{remote_script_path}"')  # 清空文件

        for i in range(0, len(encoded_script), chunk_size):
            chunk = encoded_script[i:i + chunk_size]
            cmd = f'printf "%s" "{chunk}" >> "{remote_script_path}.b64"'
            _, _, stderr = ssh.exec_command(cmd)
            error = stderr.read().decode().strip()
            if error:
                error_msg = f"写入SFTP修复脚本块失败: {error}"
                print(error_msg)
                result['message'] = error_msg
                return result

        # 解码脚本文件
        _, _, stderr = ssh.exec_command(f'base64 -d "{remote_script_path}.b64" > "{remote_script_path}" && rm "{remote_script_path}.b64"')
        error = stderr.read().decode().strip()
        if error:
            error_msg = f"解码SFTP修复脚本失败: {error}"
            print(error_msg)
            result['message'] = error_msg
            return result

        # 设置脚本执行权限
        ssh.exec_command(f'chmod +x "{remote_script_path}"')
        result['details'].append("✅ SFTP修复脚本上传成功")

        # 执行Python SFTP修复脚本
        print(f"执行SFTP修复脚本...")
        _, stdout, stderr = ssh.exec_command(f'python3 "{remote_script_path}"')

        output = stdout.read().decode().strip()
        error = stderr.read().decode().strip()

        print(f"SFTP修复脚本输出: {output}")
        result['details'].append(f"脚本执行输出: {output[:300]}...")  # 限制输出长度

        if error:
            print(f"SFTP修复脚本错误: {error}")
            result['details'].append(f"脚本执行错误: {error[:200]}...")

        # 清理临时脚本
        ssh.exec_command(f'rm -f "{remote_script_path}"')

        # 分析修复结果
        if "🎉 SFTP配置修复成功" in output:
            result['success'] = True
            result['message'] = "SFTP配置修复成功"
            result['details'].append("✅ SFTP配置修复完全成功")
            result['ssh_restarted'] = "SSH服务重启" in output
            print("✅ SFTP配置修复成功")
        elif "⚠️ SFTP配置已完成，但连接测试失败" in output:
            result['success'] = True  # 配置完成但测试失败
            result['message'] = "SFTP配置已完成，但连接测试失败"
            result['details'].append("⚠️ SFTP配置完成但测试失败，可能需要检查防火墙或网络")
            result['ssh_restarted'] = "SSH服务重启" in output
            print("⚠️ SFTP配置已完成，但连接测试失败")
        elif "SFTP子系统配置" in output and "✅" in output:
            result['success'] = True
            result['message'] = "SFTP配置部分成功"
            result['details'].append("✅ SFTP子系统配置已更新")
            result['ssh_restarted'] = "SSH服务重启" in output
            print("✅ SFTP配置部分成功")
        else:
            result['message'] = "SFTP配置修复可能未完全成功"
            result['details'].append("⚠️ 修复脚本执行完成，但结果不明确")
            print(f"⚠️ SFTP配置修复可能未完全成功")

        return result

    except Exception as e:
        error_msg = f"修复SFTP配置失败: {e}"
        print(error_msg)
        result['message'] = error_msg
        result['details'].append(f"❌ 异常错误: {str(e)}")
        return result


def setup_sudo_nopasswd(ssh, username, password):
    """设置sudo无密码权限 - 增强版本"""
    result = {
        'success': False,
        'message': '',
        'details': [],
        'verified': False
    }

    try:
        print(f"正在为用户 {username} 设置sudo无密码权限...")
        result['details'].append(f"开始为用户 {username} 设置sudo无密码权限")

        # 读取本地的Python sudo设置脚本
        python_script_path = 'packages/common/setup_sudo_nopasswd.py'
        if not os.path.exists(python_script_path):
            error_msg = f"找不到sudo设置Python脚本文件: {python_script_path}"
            print(f"错误：{error_msg}")
            result['message'] = error_msg
            return result

        with open(python_script_path, 'r', encoding='utf-8') as f:
            python_script = f.read()

        result['details'].append("✅ 本地sudo设置脚本读取成功")

        # 上传Python脚本到远程服务器
        remote_script_path = f'/tmp/setup_sudo_nopasswd.py'

        # 使用base64编码传输脚本
        import base64
        encoded_script = base64.b64encode(python_script.encode('utf-8')).decode('utf-8')

        # 分块写入脚本
        chunk_size = 4000
        ssh.exec_command(f'> "{remote_script_path}"')  # 清空文件

        for i in range(0, len(encoded_script), chunk_size):
            chunk = encoded_script[i:i + chunk_size]
            cmd = f'printf "%s" "{chunk}" >> "{remote_script_path}.b64"'
            _, _, stderr = ssh.exec_command(cmd)
            error = stderr.read().decode().strip()
            if error:
                error_msg = f"写入脚本块失败: {error}"
                print(error_msg)
                result['message'] = error_msg
                return result

        # 解码脚本文件
        _, _, stderr = ssh.exec_command(f'base64 -d "{remote_script_path}.b64" > "{remote_script_path}" && rm "{remote_script_path}.b64"')
        error = stderr.read().decode().strip()
        if error:
            error_msg = f"解码脚本失败: {error}"
            print(error_msg)
            result['message'] = error_msg
            return result

        # 设置脚本执行权限
        ssh.exec_command(f'chmod +x "{remote_script_path}"')
        result['details'].append("✅ sudo设置脚本上传成功")

        # 设置环境变量并执行Python脚本
        env_vars = f'SSH_USERNAME="{username}" SSH_PASSWORD="{password}"'
        cmd = f'{env_vars} python3 "{remote_script_path}"'

        print(f"执行sudo无密码设置脚本...")
        _, stdout, stderr = ssh.exec_command(cmd)

        output = stdout.read().decode().strip()
        error = stderr.read().decode().strip()

        print(f"脚本输出: {output}")
        result['details'].append(f"脚本执行输出: {output[:200]}...")  # 限制输出长度

        if error:
            print(f"脚本错误: {error}")
            result['details'].append(f"脚本执行错误: {error[:200]}...")

        # 清理临时脚本
        ssh.exec_command(f'rm -f "{remote_script_path}"')

        # 验证sudo无密码是否设置成功
        print("验证sudo无密码设置...")
        _, stdout, stderr = ssh.exec_command(f'sudo -n whoami')
        verify_result = stdout.read().decode().strip()

        if verify_result == 'root':
            result['success'] = True
            result['verified'] = True
            result['message'] = "sudo无密码权限设置并验证成功"
            result['details'].append("✅ sudo无密码验证成功")
            print("✅ sudo无密码设置成功")
        else:
            result['success'] = True  # 脚本执行成功
            result['verified'] = False
            result['message'] = "sudo无密码权限设置完成，但验证失败（可能需要重新登录）"
            result['details'].append(f"⚠️ sudo无密码验证失败: {verify_result}")
            print(f"⚠️ sudo无密码验证失败: {verify_result}")

        return result

    except Exception as e:
        error_msg = f"设置sudo无密码失败: {e}"
        print(error_msg)
        result['message'] = error_msg
        result['details'].append(f"❌ 异常错误: {str(e)}")
        return result


def get_remote_file_list_ssh(ssh, upload_dir):
    """获取远程服务器上的文件列表（使用SSH命令）"""
    remote_files = {}

    try:
        # 使用find命令获取所有相关文件的信息
        find_cmd = f'find {upload_dir} -type f \\( -name "*.py" -o -name "*.sh" \\) -exec stat -c "%n|%s|%Y" {{}} \\; 2>/dev/null'
        _, stdout, stderr = ssh.exec_command(find_cmd)
        output = stdout.read().decode().strip()

        if output:
            for line in output.split('\n'):
                if '|' in line:
                    parts = line.split('|')
                    if len(parts) == 3:
                        file_path, size, mtime = parts
                        # 计算相对路径
                        rel_path = os.path.relpath(file_path, upload_dir)
                        remote_files[rel_path] = {
                            'size': int(size),
                            'mtime': float(mtime),
                            'path': file_path
                        }

    except Exception as e:
        print(f"获取远程文件列表失败: {e}")

    return remote_files


def get_remote_file_list(sftp, upload_dir):
    """获取远程服务器上的文件列表（SFTP版本）"""
    remote_files = {}

    try:
        # 检查主安装程序
        try:
            stat = sftp.stat(f'{upload_dir}/main_installer.py')
            remote_files['main_installer.py'] = {
                'size': stat.st_size,
                'mtime': stat.st_mtime,
                'path': f'{upload_dir}/main_installer.py'
            }
        except FileNotFoundError:
            pass

        # 递归获取packages目录下的文件
        def scan_remote_directory(remote_path, relative_base=''):
            try:
                for item in sftp.listdir_attr(remote_path):
                    item_path = f'{remote_path}/{item.filename}'
                    relative_path = f'{relative_base}/{item.filename}' if relative_base else item.filename

                    if item.st_mode & 0o040000:  # 是目录
                        scan_remote_directory(item_path, relative_path)
                    else:  # 是文件
                        if item.filename.endswith(('.py', '.sh')):
                            remote_files[f'packages/{relative_path}'] = {
                                'size': item.st_size,
                                'mtime': item.st_mtime,
                                'path': item_path
                            }
            except FileNotFoundError:
                pass

        # 扫描packages目录
        scan_remote_directory(f'{upload_dir}/packages')

    except Exception as e:
        print(f"获取远程文件列表失败: {e}")

    return remote_files


def sync_files_intelligently_ssh(ssh, upload_dir, local_files, remote_files):
    """智能同步文件：只删除多余的和修改的文件（SSH版本）"""
    files_to_upload = []
    files_to_delete = []
    files_to_skip = []

    # 1. 检查本地文件，决定是否需要上传
    for local_rel_path, local_info in local_files.items():
        local_path = local_info['path']
        remote_path = local_info['remote_path']

        if local_rel_path in remote_files:
            remote_info = remote_files[local_rel_path]
            # 比较文件大小和修改时间
            if (local_info['size'] != remote_info['size'] or
                abs(local_info['mtime'] - remote_info['mtime']) > 2):
                # 文件已修改，需要重新上传
                files_to_upload.append(local_info)
            else:
                # 文件未修改，跳过
                files_to_skip.append(local_rel_path)
        else:
            # 新文件，需要上传
            files_to_upload.append(local_info)

    # 2. 检查远程文件，找出多余的文件
    for remote_rel_path, remote_info in remote_files.items():
        if remote_rel_path not in local_files:
            # 远程有但本地没有的文件，需要删除
            files_to_delete.append(remote_info)

    return files_to_upload, files_to_delete, files_to_skip


def sync_files_intelligently(sftp, upload_dir, local_files, remote_files):
    """智能同步文件：只删除多余的和修改的文件（SFTP版本）"""
    files_to_upload = []
    files_to_delete = []
    files_to_skip = []

    # 1. 检查本地文件，决定是否需要上传
    for local_rel_path, local_info in local_files.items():
        local_path = local_info['path']
        remote_path = local_info['remote_path']

        if local_rel_path in remote_files:
            remote_info = remote_files[local_rel_path]
            # 比较文件大小和修改时间
            if (local_info['size'] != remote_info['size'] or
                abs(local_info['mtime'] - remote_info['mtime']) > 2):
                # 文件已修改，需要重新上传
                files_to_upload.append(local_info)
            else:
                # 文件未修改，跳过
                files_to_skip.append(local_rel_path)
        else:
            # 新文件，需要上传
            files_to_upload.append(local_info)

    # 2. 检查远程文件，找出多余的文件
    for remote_rel_path, remote_info in remote_files.items():
        if remote_rel_path not in local_files:
            # 远程有但本地没有的文件，需要删除
            files_to_delete.append(remote_info)

    return files_to_upload, files_to_delete, files_to_skip


@app.route('/api/ssh/upload-with-progress', methods=['POST'])
def upload_packages_with_progress():
    """上传packages目录到远程服务器 - 先设置sudo无密码，然后用SFTP上传"""
    data = request.json
    host = data.get('host')
    username = data.get('username')
    password = data.get('password')

    try:
        # 建立SSH连接
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        ssh.connect(host, username=username, password=password, timeout=10)

        upload_dir = f'/home/<USER>/server_install'

        # 步骤1: 设置sudo无密码
        socketio.emit('upload_progress', {
            'percentage': 2,
            'status': '正在设置sudo无密码权限...',
            'current': 0,
            'total': 0,
            'currentFile': 'setup_sudo_nopasswd.py',
            'speed': '0 KB/s'
        })

        sudo_result = setup_sudo_nopasswd(ssh, username, password)
        if not sudo_result['success']:
            raise Exception(f"设置sudo无密码失败: {sudo_result['message']}")

        # 发送sudo设置详细状态
        socketio.emit('upload_progress', {
            'percentage': 4,
            'status': f"✅ {sudo_result['message']}",
            'current': 0,
            'total': 0,
            'currentFile': '',
            'speed': '0 KB/s'
        })

        # 步骤2: 尝试建立SFTP连接
        socketio.emit('upload_progress', {
            'percentage': 5,
            'status': '正在建立SFTP连接...',
            'current': 0,
            'total': 0,
            'currentFile': '',
            'speed': '0 KB/s'
        })

        try:
            sftp = ssh.open_sftp()
            use_sftp = True
            socketio.emit('upload_progress', {
                'percentage': 8,
                'status': '✅ SFTP连接成功，使用SFTP上传...',
                'current': 0,
                'total': 0,
                'currentFile': '',
                'speed': '0 KB/s'
            })
        except Exception as e:
            print(f"SFTP连接失败: {e}")

            # 尝试修复SFTP配置
            socketio.emit('upload_progress', {
                'percentage': 6,
                'status': '🔧 SFTP连接失败，正在尝试修复配置...',
                'current': 0,
                'total': 0,
                'currentFile': 'fix_sftp_config.py',
                'speed': '0 KB/s'
            })

            sftp_result = fix_sftp_config(ssh, username, password)
            if sftp_result['success']:
                # 修复成功，重新尝试SFTP连接
                socketio.emit('upload_progress', {
                    'percentage': 7,
                    'status': f"🔄 {sftp_result['message']}，重新尝试连接...",
                    'current': 0,
                    'total': 0,
                    'currentFile': '',
                    'speed': '0 KB/s'
                })

                try:
                    # 如果SSH服务重启了，需要等待更长时间
                    wait_time = 8 if sftp_result['ssh_restarted'] else 3
                    time.sleep(wait_time)

                    # 重新建立SSH连接（因为SSH服务可能重启了）
                    ssh.close()
                    ssh = paramiko.SSHClient()
                    ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
                    ssh.connect(host, username=username, password=password, timeout=15)

                    # 尝试SFTP连接
                    sftp = ssh.open_sftp()
                    use_sftp = True
                    socketio.emit('upload_progress', {
                        'percentage': 8,
                        'status': '🎉 SFTP修复成功！使用SFTP上传...',
                        'current': 0,
                        'total': 0,
                        'currentFile': '',
                        'speed': '0 KB/s'
                    })
                    print("✅ SFTP修复成功，使用SFTP上传")

                except Exception as retry_e:
                    print(f"SFTP修复后仍然连接失败: {retry_e}")
                    use_sftp = False
                    socketio.emit('upload_progress', {
                        'percentage': 8,
                        'status': '⚠️ SFTP修复后仍不可用，使用SSH命令上传...',
                        'current': 0,
                        'total': 0,
                        'currentFile': '',
                        'speed': '0 KB/s'
                    })
            else:
                # 修复失败，使用SSH命令方式
                use_sftp = False
                socketio.emit('upload_progress', {
                    'percentage': 8,
                    'status': f"⚠️ {sftp_result['message']}，使用SSH命令上传...",
                    'current': 0,
                    'total': 0,
                    'currentFile': '',
                    'speed': '0 KB/s'
                })

        # 步骤3: 分析文件变化
        socketio.emit('upload_progress', {
            'percentage': 10,
            'status': '正在分析文件变化...',
            'current': 0,
            'total': 0,
            'currentFile': '',
            'speed': '0 KB/s'
        })

        # 创建远程目录结构
        if use_sftp:
            try:
                sftp.mkdir(upload_dir)
            except:
                pass  # 目录可能已存在
            try:
                sftp.mkdir(f'{upload_dir}/packages')
            except:
                pass
            # 获取远程文件列表（SFTP方式）
            remote_files = get_remote_file_list(sftp, upload_dir)
        else:
            # 使用SSH命令创建目录
            ssh.exec_command(f'mkdir -p {upload_dir}/packages')
            # 获取远程文件列表（SSH命令方式）
            remote_files = get_remote_file_list_ssh(ssh, upload_dir)

        # 收集本地文件信息
        local_files = {}

        # 主安装程序
        if os.path.exists('main_installer.py'):
            local_stat = os.stat('main_installer.py')
            local_files['main_installer.py'] = {
                'path': 'main_installer.py',
                'remote_path': f'{upload_dir}/main_installer.py',
                'size': local_stat.st_size,
                'mtime': local_stat.st_mtime
            }

        # packages目录中的所有文件（包括common目录下的通用工具模块）
        for root, _, files in os.walk(PACKAGES_DIR):
            for file in files:
                local_path = os.path.join(root, file)
                rel_path = os.path.relpath(local_path, PACKAGES_DIR)

                # 检查是否应该上传此文件
                should_upload = False

                # 1. 脚本文件总是上传
                if file.endswith(('.py', '.sh')):
                    should_upload = True

                # 2. common/resources 目录下的所有文件都上传（资源文件）
                elif rel_path.startswith('common/resources/'):
                    should_upload = True

                if should_upload:
                    local_stat = os.stat(local_path)
                    local_files[f'packages/{rel_path}'] = {
                        'path': local_path,
                        'remote_path': f'{upload_dir}/packages/{rel_path}',
                        'size': local_stat.st_size,
                        'mtime': local_stat.st_mtime
                    }

        # 智能同步文件（根据连接方式选择）
        if use_sftp:
            files_to_upload, files_to_delete, files_to_skip = sync_files_intelligently(
                sftp, upload_dir, local_files, remote_files
            )
        else:
            files_to_upload, files_to_delete, files_to_skip = sync_files_intelligently_ssh(
                ssh, upload_dir, local_files, remote_files
            )

        total_files = len(files_to_upload)
        delete_files = len(files_to_delete)
        skipped_files = len(files_to_skip)
        total_size = sum(f['size'] for f in files_to_upload)

        # 1. 先删除多余的文件
        if files_to_delete:
            socketio.emit('upload_progress', {
                'percentage': 5,
                'status': f'正在清理 {delete_files} 个多余文件...',
                'current': 0,
                'total': total_files,
                'currentFile': '清理中...',
                'speed': '0 KB/s',
                'skipped': skipped_files
            })

            for file_info in files_to_delete:
                try:
                    if use_sftp:
                        sftp.remove(file_info['path'])
                    else:
                        ssh.exec_command(f'rm -f "{file_info["path"]}"')
                    print(f"已删除多余文件: {file_info['path']}")
                except Exception as e:
                    print(f"删除文件失败: {file_info['path']}, 错误: {e}")

        # 2. 发送上传进度
        if total_files > 0:
            if skipped_files > 0 or delete_files > 0:
                status_msg = f'智能同步：跳过 {skipped_files} 个，删除 {delete_files} 个，上传 {total_files} 个文件'
            else:
                status_msg = f'开始上传 {total_files} 个文件'
        else:
            status_msg = f'所有文件已是最新，无需上传 (跳过 {skipped_files} 个文件)'

        socketio.emit('upload_progress', {
            'percentage': 10,
            'status': status_msg,
            'current': 0,
            'total': total_files,
            'currentFile': '准备中...',
            'speed': '0 KB/s',
            'skipped': skipped_files,
            'deleted': delete_files
        })

        uploaded_files = []
        uploaded_size = 0
        start_time = time.time()

        # 如果没有文件需要上传，直接完成
        if total_files == 0:
            # 关闭连接
            if use_sftp:
                sftp.close()
            ssh.close()

            # 发送完成信号
            socketio.emit('upload_complete', {
                'success': True,
                'totalFiles': 0,
                'uploadDir': upload_dir,
                'totalSize': 0,
                'totalTime': '0.1s',
                'avgSpeed': '0 B/s',
                'message': '所有文件已是最新状态'
            })

            return jsonify({
                'success': True,
                'message': f'智能同步完成：所有文件已是最新状态 (跳过 {skipped_files} 个文件，删除 {delete_files} 个多余文件)',
                'files': [],
                'upload_dir': upload_dir,
                'performance': {
                    'totalSize': 0,
                    'totalTime': 0.1,
                    'avgSpeed': '0 B/s',
                    'skipped': skipped_files,
                    'deleted': delete_files
                }
            })

        # 使用SFTP的上传函数
        def upload_file_sftp(local_path, remote_path, file_index, file_size):
            try:
                # 创建远程目录
                remote_dir = os.path.dirname(remote_path)
                try:
                    sftp.mkdir(remote_dir)
                except:
                    pass  # 目录可能已存在

                file_name = os.path.basename(local_path)
                file_start_time = time.time()

                # 发送文件开始上传的进度
                progress_percentage = 15 + int((file_index / total_files) * 80)
                socketio.emit('upload_progress', {
                    'percentage': progress_percentage,
                    'status': f'正在上传文件 ({file_index + 1}/{total_files})',
                    'current': file_index + 1,
                    'total': total_files,
                    'currentFile': file_name,
                    'speed': calculate_speed(uploaded_size, start_time)
                })

                # 使用SFTP直接上传文件
                sftp.put(local_path, remote_path)

                # 设置执行权限（如果是脚本文件）
                if remote_path.endswith(('.py', '.sh')):
                    sftp.chmod(remote_path, 0o755)

                return True
            except Exception as e:
                print(f"SFTP上传文件失败: {local_path} -> {remote_path}, 错误: {e}")
                return False

        # 使用SSH命令的上传函数
        def upload_file_ssh(local_path, remote_path, file_index, file_size):
            try:
                # 创建远程目录
                remote_dir = os.path.dirname(remote_path)
                ssh.exec_command(f'mkdir -p "{remote_dir}"')

                file_name = os.path.basename(local_path)
                file_start_time = time.time()

                # 发送文件开始上传的进度
                progress_percentage = 10 + int((file_index / total_files) * 80)
                socketio.emit('upload_progress', {
                    'percentage': progress_percentage,
                    'status': f'正在上传文件 ({file_index + 1}/{total_files})',
                    'current': file_index + 1,
                    'total': total_files,
                    'currentFile': file_name,
                    'speed': calculate_speed(uploaded_size, start_time)
                })

                # 使用base64编码传输文件内容
                with open(local_path, 'rb') as f:
                    content = f.read()

                import base64
                encoded_content = base64.b64encode(content).decode('utf-8')

                # 将base64内容写入临时文件，然后解码
                temp_file = f'{remote_path}.tmp'

                # 分小块写入，避免命令行过长
                chunk_size = 4000  # 4KB chunks
                ssh.exec_command(f'> "{temp_file}"')  # 清空临时文件

                for i in range(0, len(encoded_content), chunk_size):
                    chunk = encoded_content[i:i + chunk_size]
                    # 使用printf避免echo的限制
                    cmd = f'printf "%s" "{chunk}" >> "{temp_file}"'
                    _, _, stderr = ssh.exec_command(cmd)

                    error = stderr.read().decode().strip()
                    if error:
                        print(f"写入块失败: {error}")
                        return False

                # 解码并移动到最终位置
                _, _, stderr = ssh.exec_command(f'base64 -d "{temp_file}" > "{remote_path}" && rm "{temp_file}"')
                error = stderr.read().decode().strip()
                if error:
                    print(f"解码文件失败: {error}")
                    return False

                # 设置执行权限（如果是脚本文件）
                if remote_path.endswith(('.py', '.sh')):
                    ssh.exec_command(f'chmod 755 "{remote_path}"')

                return True
            except Exception as e:
                print(f"SSH上传文件失败: {local_path} -> {remote_path}, 错误: {e}")
                return False

        def calculate_speed(bytes_transferred, start_time):
            """计算上传速度"""
            elapsed = time.time() - start_time
            if elapsed > 0:
                speed_bps = bytes_transferred / elapsed
                if speed_bps > 1024 * 1024:
                    return f"{speed_bps / (1024 * 1024):.1f} MB/s"
                elif speed_bps > 1024:
                    return f"{speed_bps / 1024:.1f} KB/s"
                else:
                    return f"{speed_bps:.0f} B/s"
            return "0 B/s"

        # 上传所有文件
        for index, file_info in enumerate(files_to_upload):
            local_path = file_info['path']
            remote_path = file_info['remote_path']
            file_size = file_info['size']

            # 根据连接方式选择上传函数
            if use_sftp:
                success = upload_file_sftp(local_path, remote_path, index, file_size)
            else:
                success = upload_file_ssh(local_path, remote_path, index, file_size)

            if success:
                uploaded_files.append(os.path.relpath(local_path, '.'))
                uploaded_size += file_size

        # 关闭连接
        if use_sftp:
            sftp.close()
        ssh.close()

        # 发送完成信号
        total_time = time.time() - start_time
        final_speed = calculate_speed(uploaded_size, start_time)

        socketio.emit('upload_complete', {
            'success': True,
            'totalFiles': len(uploaded_files),
            'uploadDir': upload_dir,
            'totalSize': uploaded_size,
            'totalTime': f"{total_time:.1f}s",
            'avgSpeed': final_speed,
            'skipped': skipped_files,
            'deleted': delete_files
        })

        # 构建详细的同步报告
        sync_actions = []
        if len(uploaded_files) > 0:
            sync_actions.append(f"上传 {len(uploaded_files)} 个")
        if skipped_files > 0:
            sync_actions.append(f"跳过 {skipped_files} 个")
        if delete_files > 0:
            sync_actions.append(f"删除 {delete_files} 个")

        sync_summary = "、".join(sync_actions) if sync_actions else "无变化"

        return jsonify({
            'success': True,
            'message': f'智能同步完成：{sync_summary} (耗时: {total_time:.1f}s, 平均速度: {final_speed})',
            'files': uploaded_files,
            'upload_dir': upload_dir,
            'performance': {
                'totalSize': uploaded_size,
                'totalTime': total_time,
                'avgSpeed': final_speed,
                'skipped': skipped_files,
                'deleted': delete_files,
                'uploaded': len(uploaded_files)
            }
        })

    except Exception as e:
        # 发送错误信号
        socketio.emit('upload_complete', {
            'success': False,
            'error': str(e)
        })
        return jsonify({'success': False, 'message': f'上传失败: {str(e)}'})

@app.route('/api/install/prepare', methods=['POST'])
def prepare_install():
    """准备安装队列文件"""
    data = request.json
    host = data.get('host')
    username = data.get('username')
    password = data.get('password')
    packages = data.get('packages', [])
    operation = data.get('operation', 'install')  # 默认为安装，可以是 'uninstall'

    try:
        # 生成安装/卸载队列文件
        queue_data = {
            'packages': packages,
            'operation': operation,
            'timestamp': str(datetime.now())
        }

        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        ssh.connect(host, username=username, password=password)

        # 上传队列文件 - 使用SSH命令方式
        upload_dir = f'/home/<USER>/server_install'
        queue_file = f'{upload_dir}/install_queue.json'

        # 将JSON数据编码并通过SSH命令写入文件
        import base64
        queue_json = json.dumps(queue_data, indent=2, ensure_ascii=False)
        encoded_content = base64.b64encode(queue_json.encode('utf-8')).decode('utf-8')

        command = f'echo "{encoded_content}" | base64 -d > {queue_file}'
        _, stdout, stderr = ssh.exec_command(command)

        # 检查队列文件是否创建成功
        _, stdout, stderr = ssh.exec_command(f'test -f {queue_file} && echo "OK"')
        result = stdout.read().decode().strip()
        if result != "OK":
            return jsonify({'success': False, 'message': '队列文件创建失败'})

        ssh.close()

        return jsonify({
            'success': True,
            'message': '安装队列文件已准备完成',
            'queue_file': queue_file,
            'packages_count': len(packages)
        })

    except Exception as e:
        return jsonify({'success': False, 'message': f'准备安装队列失败: {str(e)}'})

@app.route('/api/uninstall/prepare', methods=['POST'])
def prepare_uninstall():
    """准备卸载队列文件"""
    data = request.json
    host = data.get('host')
    username = data.get('username')
    password = data.get('password')
    packages = data.get('packages', [])

    try:
        # 生成卸载队列文件
        queue_data = {
            'packages': packages,
            'operation': 'uninstall',
            'timestamp': str(datetime.now())
        }

        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        ssh.connect(host, username=username, password=password)

        # 上传队列文件 - 使用SSH命令方式
        upload_dir = f'/home/<USER>/server_install'
        queue_file = f'{upload_dir}/uninstall_queue.json'

        # 将JSON数据编码并通过SSH命令写入文件
        import base64
        queue_json = json.dumps(queue_data, indent=2, ensure_ascii=False)
        encoded_content = base64.b64encode(queue_json.encode('utf-8')).decode('utf-8')

        command = f'echo "{encoded_content}" | base64 -d > {queue_file}'
        _, stdout, stderr = ssh.exec_command(command)

        # 检查队列文件是否创建成功
        _, stdout, stderr = ssh.exec_command(f'test -f {queue_file} && echo "OK"')
        result = stdout.read().decode().strip()
        if result != "OK":
            return jsonify({'success': False, 'message': '卸载队列文件创建失败'})

        ssh.close()

        return jsonify({
            'success': True,
            'message': '卸载队列文件已准备完成',
            'queue_file': queue_file,
            'packages_count': len(packages)
        })

    except Exception as e:
        return jsonify({'success': False, 'message': f'准备卸载队列失败: {str(e)}'})

@app.route('/api/packages/update_record', methods=['POST'])
def update_packages_record():
    """更新已安装包记录"""
    data = request.json
    host = data.get('host')
    username = data.get('username')
    password = data.get('password')
    packages = data.get('packages', [])
    operation = data.get('operation', 'add')  # 'add' 或 'remove'

    try:
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        ssh.connect(host, username=username, password=password)

        upload_dir = f'/home/<USER>/server_install'

        # 更新已安装包记录
        update_installed_packages_record(ssh, upload_dir, packages, operation)

        ssh.close()

        return jsonify({
            'success': True,
            'message': f'已安装包记录已更新 ({operation})',
            'packages_count': len(packages)
        })

    except Exception as e:
        return jsonify({'success': False, 'message': f'更新记录失败: {str(e)}'})

@app.route('/api/install/execute', methods=['POST'])
def execute_install():
    """执行安装队列"""
    data = request.json
    host = data.get('host')
    username = data.get('username')
    password = data.get('password')
    packages = data.get('packages', [])
    operation = data.get('operation', 'install')  # 默认为安装，可以是 'uninstall'

    try:
        # 生成安装/卸载队列文件
        queue_data = {
            'packages': packages,
            'operation': operation,
            'timestamp': str(datetime.now())
        }

        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        ssh.connect(host, username=username, password=password)

        # 上传队列文件 - 使用SSH命令方式
        upload_dir = f'/home/<USER>/server_install'
        queue_file = f'{upload_dir}/install_queue.json'

        # 将JSON数据编码并通过SSH命令写入文件
        import base64
        queue_json = json.dumps(queue_data, indent=2, ensure_ascii=False)
        encoded_content = base64.b64encode(queue_json.encode('utf-8')).decode('utf-8')

        command = f'echo "{encoded_content}" | base64 -d > {queue_file}'
        _, stdout, stderr = ssh.exec_command(command)

        # 检查队列文件是否创建成功
        _, stdout, stderr = ssh.exec_command(f'test -f {queue_file} && echo "OK"')
        result = stdout.read().decode().strip()
        if result != "OK":
            return jsonify({'success': False, 'message': '队列文件创建失败'})

        # 执行安装/卸载命令
        operation_arg = 'uninstall' if operation == 'uninstall' else 'install'
        # 传递用户名和密码参数以避免手动输入
        command = f'cd {upload_dir} && python3 main_installer.py install_queue.json {operation_arg} "{username}" "{password}"'
        _, stdout, stderr = ssh.exec_command(command, timeout=300)  # 增加超时时间到5分钟

        # 获取执行结果，确保完整读取
        output = stdout.read().decode('utf-8', errors='ignore').strip()
        error = stderr.read().decode('utf-8', errors='ignore').strip()

        # 如果没有输出，提供默认信息
        if not output and not error:
            output = f"命令执行完成: {command}\n安装队列包含 {len(packages)} 个包"

        # 如果是安装操作且成功，更新已安装包记录
        success_indicators = ['success', '成功', '✅', '🎉', '安装完成']
        is_success = any(indicator in output.lower() for indicator in success_indicators) or not error
        if operation == 'install' and is_success:
            update_installed_packages_record(ssh, upload_dir, packages, 'add')

        ssh.close()

        return jsonify({
            'success': True,
            'output': output,
            'error': error,
            'command': command
        })

    except Exception as e:
        return jsonify({'success': False, 'message': f'执行失败: {str(e)}'})

@app.route('/api/config/save', methods=['POST'])
def save_config():
    """保存配置"""
    data = request.json
    config_name = data.get('name', 'default')

    config_file = f'configs/{config_name}.json'
    os.makedirs('configs', exist_ok=True)

    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(data, f, indent=2, ensure_ascii=False)

    return jsonify({'success': True, 'message': '配置已保存'})

@app.route('/api/config/load/<config_name>')
def load_config(config_name):
    """加载配置"""
    config_file = f'configs/{config_name}.json'

    if os.path.exists(config_file):
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        return jsonify({'success': True, 'config': config})
    else:
        return jsonify({'success': False, 'message': '配置文件不存在'})

@app.route('/api/packages/installed', methods=['POST'])
def get_installed_packages():
    """获取已安装的软件包列表"""
    data = request.json
    host = data.get('host')
    username = data.get('username')
    password = data.get('password')

    try:
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        ssh.connect(host, username=username, password=password, timeout=10)

        upload_dir = f'/home/<USER>/server_install'

        # 1. 读取记录文件中的包列表
        recorded_packages = get_recorded_packages(ssh, upload_dir)

        # 2. 发现系统中可能存在但未记录的包
        discovered_packages = discover_unrecorded_packages(ssh, upload_dir)

        # 3. 合并包列表，去重
        all_packages = merge_package_lists(recorded_packages, discovered_packages)

        # 4. 验证每个包的实际状态
        verified_packages = []
        for pkg in all_packages:
            status = verify_package_status(ssh, upload_dir, pkg)
            pkg['status'] = status
            pkg['lastChecked'] = datetime.now().isoformat()

            # 只返回确实存在的包（排除not_installed状态的包）
            if status != 'not_installed':
                verified_packages.append(pkg)

        # 5. 更新记录文件，移除不存在的包
        update_package_records_based_on_verification(ssh, upload_dir, verified_packages)

        ssh.close()

        return jsonify({
            'success': True,
            'packages': verified_packages,
            'count': len(verified_packages),
            'discovered_count': len(discovered_packages)
        })

    except Exception as e:
        return jsonify({'success': False, 'message': f'获取已安装包列表失败: {str(e)}'})

@app.route('/api/packages/verify', methods=['POST'])
def verify_packages():
    """验证指定软件包的安装状态 - 优化版本"""
    data = request.json
    host = data.get('host')
    username = data.get('username')
    password = data.get('password')
    packages = data.get('packages', [])

    try:
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        ssh.connect(host, username=username, password=password, timeout=10)

        upload_dir = f'/home/<USER>/server_install'

        # 使用单个包验证来确保准确性
        verified_packages = []
        for pkg in packages:
            status = verify_package_status(ssh, upload_dir, pkg)
            pkg['status'] = status
            pkg['lastChecked'] = datetime.now().isoformat()
            verified_packages.append(pkg)

        ssh.close()

        return jsonify({
            'success': True,
            'packages': verified_packages
        })

    except Exception as e:
        return jsonify({'success': False, 'message': f'验证包状态失败: {str(e)}'})

@app.route('/api/packages/uninstall', methods=['POST'])
def uninstall_selected_packages():
    """卸载选中的软件包"""
    data = request.json
    host = data.get('host')
    username = data.get('username')
    password = data.get('password')
    packages = data.get('packages', [])

    if not packages:
        return jsonify({'success': False, 'message': '没有选择要卸载的软件包'})

    try:
        # 使用现有的安装执行逻辑，但设置为卸载模式
        queue_data = {
            'packages': packages,
            'operation': 'uninstall',
            'timestamp': str(datetime.now())
        }

        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        ssh.connect(host, username=username, password=password)

        upload_dir = f'/home/<USER>/server_install'
        queue_file = f'{upload_dir}/uninstall_queue.json'

        # 上传卸载队列文件
        import base64
        queue_json = json.dumps(queue_data, indent=2, ensure_ascii=False)
        encoded_content = base64.b64encode(queue_json.encode('utf-8')).decode('utf-8')

        command = f'echo "{encoded_content}" | base64 -d > {queue_file}'
        _, stdout, stderr = ssh.exec_command(command)

        # 执行卸载命令
        # 传递用户名和密码参数以避免手动输入
        command = f'cd {upload_dir} && python3 main_installer.py uninstall_queue.json uninstall "{username}" "{password}"'
        _, stdout, stderr = ssh.exec_command(command)

        # 获取执行结果
        output = stdout.read().decode()
        error = stderr.read().decode()

        # 更新已安装包记录
        success_indicators = ['success', '成功', '✅', '🎉', '卸载完成']
        is_success = any(indicator in output.lower() for indicator in success_indicators) or not error
        if is_success:
            update_installed_packages_record(ssh, upload_dir, packages, 'remove')

        ssh.close()

        return jsonify({
            'success': True,
            'output': output,
            'error': error,
            'uninstalled_count': len(packages)
        })

    except Exception as e:
        return jsonify({'success': False, 'message': f'卸载失败: {str(e)}'})

@app.route('/api/packages/check-all', methods=['POST'])
def check_all_packages_api():
    """后台执行检查所有包状态"""
    data = request.json
    host = data.get('host')
    username = data.get('username')
    password = data.get('password')

    try:
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        ssh.connect(host, username=username, password=password)

        upload_dir = f'/home/<USER>/server_install'

        # 执行检查脚本
        check_command = f'cd {upload_dir} && python3 packages/common/check_all_packages.py'
        stdin, stdout, stderr = ssh.exec_command(check_command)

        # 等待命令完成
        stdout.read()
        stderr_output = stderr.read().decode().strip()

        if stderr_output:
            print(f"检查脚本stderr: {stderr_output}")

        # 读取检查结果文件
        result_file = f'{upload_dir}/installed_packages.json'
        stdin, stdout, stderr = ssh.exec_command(f'cat {result_file}')

        result_content = stdout.read().decode().strip()
        stderr_output = stderr.read().decode().strip()

        ssh.close()

        if result_content:
            import json
            result_data = json.loads(result_content)
            installed_packages = result_data.get('packages', [])

            return jsonify({
                'success': True,
                'installedPackages': installed_packages,
                'totalPackages': result_data.get('total_packages', 0),
                'installedCount': result_data.get('installed_count', 0),
                'lastCheck': result_data.get('last_check', ''),
                'checkDurationMs': result_data.get('check_duration_ms', 0),
                'checkDurationSeconds': result_data.get('check_duration_seconds', 0)
            })
        else:
            return jsonify({
                'success': False,
                'message': '无法读取检查结果文件'
            })

    except Exception as e:
        return jsonify({'success': False, 'message': f'检查失败: {str(e)}'})

def verify_packages_batch_optimized(ssh, upload_dir, packages):
    """批量优化验证多个软件包的安装状态"""
    try:
        verified_packages = []
        current_time = datetime.now().isoformat()

        if not packages:
            return verified_packages

        # 构建批量检查命令
        batch_commands = []
        package_info = []

        for i, pkg in enumerate(packages):
            package_file = pkg.get('file', '')
            package_type = pkg.get('type', 'python')
            package_name = pkg.get('name', '')

            if not package_file:
                pkg['status'] = 'unknown'
                pkg['lastChecked'] = current_time
                verified_packages.append(pkg)
                continue

            # 为每个包构建检查命令
            if package_type == 'python':
                status_cmd = f'cd {upload_dir} && python3 packages/{package_file} status 2>/dev/null'
            else:  # shell
                status_cmd = f'cd {upload_dir} && bash packages/{package_file} status 2>/dev/null'

            # 添加分隔符和包索引，便于后续解析结果
            batch_commands.append(f'echo "===PACKAGE_{i}_START==="')
            batch_commands.append(status_cmd)
            batch_commands.append(f'echo "===PACKAGE_{i}_END==="')

            package_info.append({
                'index': i,
                'package': pkg,
                'name': package_name
            })

        if not batch_commands:
            return verified_packages

        # 执行批量命令
        combined_command = ' && '.join(batch_commands)
        _, stdout, stderr = ssh.exec_command(combined_command, timeout=30)

        output = stdout.read().decode('utf-8', errors='ignore')
        error_output = stderr.read().decode('utf-8', errors='ignore')

        # 解析批量结果
        package_outputs = parse_batch_output(output, len(package_info))

        # 处理每个包的结果
        for info in package_info:
            pkg = info['package']
            pkg_output = package_outputs.get(info['index'], '')

            # 简化的状态判断逻辑
            status = determine_status_from_output(pkg_output, info['name'])

            pkg['status'] = status
            pkg['lastChecked'] = current_time
            verified_packages.append(pkg)

        return verified_packages

    except Exception as e:
        print(f"批量验证包状态失败: {e}")
        # 如果批量验证失败，回退到基础状态
        for pkg in packages:
            pkg['status'] = 'unknown'
            pkg['lastChecked'] = datetime.now().isoformat()
            verified_packages.append(pkg)
        return verified_packages


def parse_batch_output(output, package_count):
    """解析批量命令的输出结果"""
    package_outputs = {}

    try:
        lines = output.split('\n')
        current_package = None
        current_output = []

        for line in lines:
            if line.startswith('===PACKAGE_') and line.endswith('_START==='):
                # 提取包索引
                package_index = int(line.split('_')[1])
                current_package = package_index
                current_output = []
            elif line.startswith('===PACKAGE_') and line.endswith('_END==='):
                # 保存当前包的输出
                if current_package is not None:
                    package_outputs[current_package] = '\n'.join(current_output)
                current_package = None
                current_output = []
            elif current_package is not None:
                current_output.append(line)

        return package_outputs

    except Exception as e:
        print(f"解析批量输出失败: {e}")
        return {}


def determine_status_from_output(output, package_name):
    """从输出中确定包状态 - 优化版本"""
    try:
        if not output:
            return 'unknown'

        output_lower = output.lower()

        # 基于彩色圆圈状态的统一检查
        if '🟢 状态:' in output:
            if '运行中' in output or 'running' in output_lower or '正常运行' in output:
                return 'running'
            else:
                return 'installed'
        elif '🟡 状态:' in output:
            return 'stopped'
        elif '🔴 状态:' in output:
            return 'not_installed'

        # 兼容不同的状态格式
        if '🟢' in output:
            if '运行中' in output or 'running' in output_lower or '正常运行' in output:
                return 'running'
            else:
                return 'installed'
        elif '🟡' in output:
            return 'stopped'
        elif '🔴' in output:
            return 'not_installed'

        # 通用状态检查
        # 未安装/未生效状态
        if ('not found' in output_lower or 'not installed' in output_lower or '未安装' in output or
            '使用官方源' in output or '仍在运行中' in output):
            return 'not_installed'
        # 运行中状态
        elif ('active' in output_lower or 'running' in output_lower or '运行中' in output or
              '正常运行' in output):
            return 'running'
        # 停止状态
        elif ('inactive' in output_lower or 'stopped' in output_lower or '已停止' in output or
              '未运行' in output):
            return 'stopped'
        # 已安装/已生效状态
        elif ('installed' in output_lower or '已安装' in output or '已禁用' in output or
              '已使用阿里云源' in output or '阿里云源' in output):
            return 'installed'

        # 如果有输出但无法确定状态，认为已安装
        return 'installed' if output.strip() else 'unknown'

    except Exception as e:
        print(f"判断状态失败: {e}")
        return 'unknown'


def verify_package_status(ssh, upload_dir, package):
    """验证单个软件包的安装状态 - 简化版本"""
    try:
        package_file = package.get('file', '')
        package_type = package.get('type', 'python')
        package_name = package.get('name', '')

        if not package_file:
            return 'unknown'

        # 只检查包脚本的status命令，移除复杂的系统级验证
        if package_type == 'python':
            check_command = f'cd {upload_dir} && python3 packages/{package_file} status 2>/dev/null'
        else:  # shell
            check_command = f'cd {upload_dir} && bash packages/{package_file} status 2>/dev/null'

        _, stdout, stderr = ssh.exec_command(check_command)
        output = stdout.read().decode().strip()

        # 使用简化的状态判断
        return determine_status_from_output(output, package_name)

    except Exception as e:
        print(f"验证包状态失败: {e}")
        return 'unknown'


def check_package_script_status(ssh, upload_dir, package_file, package_type):
    """检查包脚本的status命令输出"""
    try:
        # 构建检查命令
        if package_type == 'python':
            check_command = f'cd {upload_dir} && python3 packages/{package_file} status 2>/dev/null'
        else:  # shell
            check_command = f'cd {upload_dir} && bash packages/{package_file} status 2>/dev/null'

        _, stdout, stderr = ssh.exec_command(check_command)
        output = stdout.read().decode().strip()
        error = stderr.read().decode().strip()

        return {
            'output': output,
            'error': error,
            'has_output': bool(output),
            'has_error': bool(error)
        }
    except Exception as e:
        print(f"检查包脚本状态失败: {e}")
        return {'output': '', 'error': str(e), 'has_output': False, 'has_error': True}


def check_system_level_status(ssh, package_name, package_file):
    """进行系统级状态检查"""
    try:
        status_info = {
            'services': [],
            'processes': [],
            'installed_packages': [],
            'files_exist': False
        }

        # 1. 检查可能的服务状态（基于包名推测）
        service_names = generate_possible_service_names(package_name, package_file)
        for service_name in service_names:
            service_status = check_service_status(ssh, service_name)
            if service_status['exists']:
                status_info['services'].append(service_status)

        # 2. 检查相关进程
        process_names = generate_possible_process_names(package_name, package_file)
        for process_name in process_names:
            process_status = check_process_status(ssh, process_name)
            if process_status['running']:
                status_info['processes'].append(process_status)

        # 3. 检查系统包管理器中的包
        pkg_names = generate_possible_package_names(package_name, package_file)
        for pkg_name in pkg_names:
            pkg_status = check_system_package_status(ssh, pkg_name)
            if pkg_status['installed']:
                status_info['installed_packages'].append(pkg_status)

        # 4. 检查常见的安装文件/目录
        common_paths = generate_possible_install_paths(package_name, package_file)
        status_info['files_exist'] = check_installation_files(ssh, common_paths)

        return status_info

    except Exception as e:
        print(f"系统级状态检查失败: {e}")
        return {'services': [], 'processes': [], 'installed_packages': [], 'files_exist': False}


def generate_possible_service_names(package_name, package_file):
    """生成可能的服务名称"""
    names = set()
    if package_name:
        names.add(package_name.lower())
        names.add(package_name.lower().replace('_', '-'))
        names.add(package_name.lower().replace('-', '_'))

    # 从文件名提取
    if package_file:
        base_name = os.path.splitext(os.path.basename(package_file))[0]
        names.add(base_name.lower())
        names.add(base_name.lower().replace('_', '-'))
        names.add(base_name.lower().replace('-', '_'))

    return list(names)


def generate_possible_process_names(package_name, package_file):
    """生成可能的进程名称"""
    return generate_possible_service_names(package_name, package_file)


def generate_possible_package_names(package_name, package_file):
    """生成可能的系统包名称"""
    return generate_possible_service_names(package_name, package_file)


def generate_possible_install_paths(package_name, package_file):
    """生成可能的安装路径"""
    paths = []
    if package_name:
        name = package_name.lower()
        paths.extend([
            f'/usr/bin/{name}',
            f'/usr/local/bin/{name}',
            f'/opt/{name}',
            f'/etc/{name}',
            f'/var/lib/{name}',
            f'/usr/share/{name}'
        ])
    return paths


def check_service_status(ssh, service_name):
    """检查systemd服务状态"""
    try:
        # 检查服务是否存在
        _, stdout, _ = ssh.exec_command(f'systemctl list-unit-files | grep "^{service_name}\\."')
        exists = bool(stdout.read().decode().strip())

        if not exists:
            return {'name': service_name, 'exists': False, 'active': False, 'enabled': False}

        # 检查服务状态
        _, stdout, _ = ssh.exec_command(f'systemctl is-active {service_name}')
        active = stdout.read().decode().strip() == 'active'

        _, stdout, _ = ssh.exec_command(f'systemctl is-enabled {service_name}')
        enabled = stdout.read().decode().strip() == 'enabled'

        return {
            'name': service_name,
            'exists': True,
            'active': active,
            'enabled': enabled
        }
    except Exception:
        return {'name': service_name, 'exists': False, 'active': False, 'enabled': False}


def check_process_status(ssh, process_name):
    """检查进程状态"""
    try:
        _, stdout, _ = ssh.exec_command(f'pgrep -f "{process_name}"')
        pids = stdout.read().decode().strip()
        running = bool(pids)

        return {
            'name': process_name,
            'running': running,
            'pids': pids.split('\n') if pids else []
        }
    except Exception:
        return {'name': process_name, 'running': False, 'pids': []}


def check_system_package_status(ssh, package_name):
    """检查系统包管理器中的包状态"""
    try:
        # 检查apt包（Debian/Ubuntu）
        _, stdout, _ = ssh.exec_command(f'dpkg -l | grep "^ii.*{package_name}"')
        apt_installed = bool(stdout.read().decode().strip())

        # 检查yum/dnf包（RHEL/CentOS/Fedora）
        _, stdout, _ = ssh.exec_command(f'rpm -qa | grep "{package_name}"')
        rpm_installed = bool(stdout.read().decode().strip())

        return {
            'name': package_name,
            'installed': apt_installed or rpm_installed,
            'apt_installed': apt_installed,
            'rpm_installed': rpm_installed
        }
    except Exception:
        return {'name': package_name, 'installed': False, 'apt_installed': False, 'rpm_installed': False}


def check_installation_files(ssh, paths):
    """检查安装文件是否存在"""
    try:
        for path in paths:
            _, stdout, _ = ssh.exec_command(f'test -e "{path}" && echo "exists"')
            if stdout.read().decode().strip() == 'exists':
                return True
        return False
    except Exception:
        return False


def determine_final_status(script_status, system_status, package_name):
    """综合判断最终状态"""
    try:
        # 1. 如果脚本明确返回了状态信息，优先使用
        if script_status['has_output'] and not script_status['has_error']:
            output = script_status['output']
            output_lower = output.lower()

            # 统一的状态检查逻辑 - 基于彩色圆圈状态
            if '🟢 状态:' in output:
                # 绿色圆圈表示完全正常/运行中
                if '运行中' in output or 'running' in output_lower:
                    return 'running'
                else:
                    return 'installed'
            elif '🟡 状态:' in output:
                # 黄色圆圈表示部分安装/已安装但未运行
                return 'stopped'
            elif '🔴 状态:' in output:
                # 红色圆圈表示未安装
                return 'not_installed'

            # 兼容旧格式的特殊处理
            if package_name == 'change_sources' or 'change_sources' in package_name:
                if '使用官方源' in output or '使用官方源' in output_lower:
                    return 'not_installed'
                elif '已使用阿里云源' in output or '阿里云源' in output_lower:
                    return 'installed'

            # 兼容其他旧格式
            if 'not found' in output_lower or 'not installed' in output_lower:
                return 'not_installed'
            elif 'active' in output_lower or 'running' in output_lower:
                return 'running'
            elif 'inactive' in output_lower or 'stopped' in output_lower:
                return 'stopped'
            elif 'installed' in output_lower:
                return 'installed'

        # 2. 检查系统级状态
        # 如果有活跃的服务，认为是运行中
        active_services = [s for s in system_status['services'] if s['active']]
        if active_services:
            return 'running'

        # 如果有运行的进程，认为是运行中
        if system_status['processes']:
            return 'running'

        # 如果有已安装的系统包或安装文件存在，认为已安装但未运行
        if system_status['installed_packages'] or system_status['files_exist']:
            return 'installed'

        # 如果有服务但未激活，认为已安装但停止
        if system_status['services']:
            return 'stopped'

        # 3. 如果脚本有错误输出，可能未安装
        if script_status['has_error']:
            error = script_status['error'].lower()
            if 'not found' in error or 'command not found' in error:
                return 'not_installed'

        # 4. 检查脚本输出中的"not found"（即使没有错误）
        if script_status['has_output']:
            output = script_status['output'].lower()
            if 'not found' in output:
                return 'not_installed'
            return 'installed'  # 有输出但无法确定具体状态
        else:
            return 'unknown'    # 无法确定状态

    except Exception as e:
        print(f"判断最终状态失败: {e}")
        return 'unknown'

def get_recorded_packages(ssh, upload_dir):
    """从记录文件中获取已安装包列表"""
    try:
        installed_file = f'{upload_dir}/installed_packages.json'
        _, stdout, _ = ssh.exec_command(f'test -f {installed_file} && cat {installed_file}')

        installed_data = stdout.read().decode().strip()
        if installed_data:
            try:
                return json.loads(installed_data)
            except json.JSONDecodeError:
                return []
        else:
            return []
    except Exception as e:
        print(f"读取记录文件失败: {e}")
        return []


def discover_unrecorded_packages(ssh, upload_dir):
    """发现系统中可能存在但未记录的包"""
    try:
        discovered = []

        # 检查packages目录中的所有脚本文件
        _, stdout, _ = ssh.exec_command(f'find {upload_dir}/packages -name "*.py" -o -name "*.sh" 2>/dev/null')
        package_files = stdout.read().decode().strip().split('\n')

        for file_path in package_files:
            if not file_path.strip():
                continue

            # 提取相对路径
            rel_path = file_path.replace(f'{upload_dir}/packages/', '')
            if not rel_path:
                continue

            # 排除 common 目录下的文件，这些是通用工具模块，不应显示在包列表中
            path_parts = rel_path.split('/')
            if path_parts[0] == 'common':
                continue

            # 构造包信息
            name = os.path.splitext(os.path.basename(rel_path))[0]
            pkg_type = 'python' if rel_path.endswith('.py') else 'shell'

            # 尝试从路径推断平台和架构
            platform = path_parts[0] if len(path_parts) > 1 else 'unknown'
            arch = path_parts[1] if len(path_parts) > 2 else 'any'

            package_info = {
                'name': name,
                'file': rel_path,
                'os': platform,
                'arch': arch,
                'type': pkg_type,
                'discovered': True  # 标记为发现的包
            }

            discovered.append(package_info)

        return discovered

    except Exception as e:
        print(f"发现未记录包失败: {e}")
        return []


def merge_package_lists(recorded_packages, discovered_packages):
    """合并包列表，去重"""
    try:
        # 使用文件路径作为唯一标识
        merged = {}

        # 添加记录的包
        for pkg in recorded_packages:
            file_key = pkg.get('file', '')
            if file_key:
                merged[file_key] = pkg

        # 添加发现的包（如果不存在）
        for pkg in discovered_packages:
            file_key = pkg.get('file', '')
            if file_key and file_key not in merged:
                merged[file_key] = pkg

        return list(merged.values())

    except Exception as e:
        print(f"合并包列表失败: {e}")
        return recorded_packages + discovered_packages


def update_package_records_based_on_verification(ssh, upload_dir, verified_packages):
    """基于验证结果更新包记录文件"""
    try:
        # 只保留确实存在的包（排除发现但不存在的包）
        valid_packages = []
        for pkg in verified_packages:
            # 移除临时标记
            if 'discovered' in pkg:
                del pkg['discovered']
            valid_packages.append(pkg)

        # 更新记录文件
        installed_file = f'{upload_dir}/installed_packages.json'
        updated_json = json.dumps(valid_packages, indent=2, ensure_ascii=False)

        import base64
        encoded_content = base64.b64encode(updated_json.encode('utf-8')).decode('utf-8')
        command = f'echo "{encoded_content}" | base64 -d > {installed_file}'
        ssh.exec_command(command)

        return True

    except Exception as e:
        print(f"更新包记录失败: {e}")
        return False


def update_installed_packages_record(ssh, upload_dir, packages, operation='add'):
    """更新已安装包记录文件"""
    try:
        installed_file = f'{upload_dir}/installed_packages.json'

        # 读取现有记录
        _, stdout, _ = ssh.exec_command(f'test -f {installed_file} && cat {installed_file} || echo "[]"')
        existing_data = stdout.read().decode().strip()

        try:
            installed_packages = json.loads(existing_data) if existing_data else []
        except json.JSONDecodeError:
            installed_packages = []

        if operation == 'add':
            # 添加新安装的包
            for pkg in packages:
                # 检查是否已存在
                existing = next((p for p in installed_packages if p.get('file') == pkg.get('file')), None)
                if not existing:
                    pkg_record = {
                        'name': pkg.get('name'),
                        'file': pkg.get('file'),
                        'os': pkg.get('os'),
                        'arch': pkg.get('arch'),
                        'type': pkg.get('type'),
                        'installTime': datetime.now().isoformat(),
                        'status': 'installed'
                    }
                    installed_packages.append(pkg_record)
        elif operation == 'remove':
            # 移除卸载的包
            files_to_remove = [pkg.get('file') for pkg in packages]
            installed_packages = [p for p in installed_packages if p.get('file') not in files_to_remove]

        # 写回文件
        import base64
        updated_json = json.dumps(installed_packages, indent=2, ensure_ascii=False)
        encoded_content = base64.b64encode(updated_json.encode('utf-8')).decode('utf-8')

        command = f'echo "{encoded_content}" | base64 -d > {installed_file}'
        ssh.exec_command(command)

        return True

    except Exception as e:
        print(f"更新已安装包记录失败: {e}")
        return False

# 存储SSH连接的字典 - 支持多标签页
ssh_connections = {}  # session_id -> {tab_id -> SSHTerminal}

class SSHTerminal:
    """SSH终端处理类"""
    def __init__(self, host, username, password, session_id, tab_id):
        self.host = host
        self.username = username
        self.password = password
        self.session_id = session_id
        self.tab_id = tab_id
        self.ssh = None
        self.channel = None
        self.running = False

    def connect(self):
        """建立SSH连接"""
        try:
            self.ssh = paramiko.SSHClient()
            self.ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            self.ssh.connect(self.host, username=self.username, password=self.password, timeout=10)

            # 创建交互式shell
            self.channel = self.ssh.invoke_shell()
            self.channel.settimeout(0.1)

            # 设置终端大小
            self.channel.resize_pty(width=80, height=24)

            return True
        except Exception as e:
            print(f"SSH连接失败: {e}")
            return False

    def start_reading(self):
        """开始读取SSH输出"""
        self.running = True

        def read_output():
            while self.running and self.channel:
                try:
                    if self.channel.recv_ready():
                        data = self.channel.recv(1024).decode('utf-8', errors='ignore')
                        if data:
                            socketio.emit('terminal_output', {'data': data, 'tabId': self.tab_id}, room=self.session_id)
                    time.sleep(0.01)
                except Exception as e:
                    print(f"读取SSH输出错误: {e}")
                    break

        thread = threading.Thread(target=read_output)
        thread.daemon = True
        thread.start()

    def send_input(self, data):
        """发送输入到SSH"""
        if self.channel:
            try:
                self.channel.send(data)
            except Exception as e:
                print(f"发送SSH输入错误: {e}")

    def resize(self, cols, rows):
        """调整终端大小"""
        if self.channel:
            try:
                self.channel.resize_pty(width=cols, height=rows)
            except Exception as e:
                print(f"调整终端大小错误: {e}")

    def close(self):
        """关闭SSH连接"""
        self.running = False
        if self.channel:
            self.channel.close()
        if self.ssh:
            self.ssh.close()

@socketio.on('connect')
def handle_connect():
    """WebSocket连接处理"""
    print(f"客户端连接: {request.sid}")

@socketio.on('disconnect')
def handle_disconnect():
    """WebSocket断开处理"""
    print(f"客户端断开: {request.sid}")
    # 清理SSH连接
    if request.sid in ssh_connections:
        for terminal in ssh_connections[request.sid].values():
            terminal.close()
        del ssh_connections[request.sid]

@socketio.on('terminal_connect')
def handle_terminal_connect(data):
    """处理终端连接请求"""
    try:
        host = data.get('host')
        username = data.get('username')
        password = data.get('password')
        tab_id = data.get('tabId')

        if not all([host, username, password, tab_id]):
            emit('terminal_error', {'message': '连接参数不完整', 'tabId': tab_id})
            return

        # 初始化session的连接字典
        if request.sid not in ssh_connections:
            ssh_connections[request.sid] = {}

        # 创建SSH终端实例
        terminal = SSHTerminal(host, username, password, request.sid, tab_id)

        if terminal.connect():
            ssh_connections[request.sid][tab_id] = terminal
            terminal.start_reading()
            emit('terminal_connected', {'message': '连接成功', 'tabId': tab_id})
        else:
            emit('terminal_error', {'message': 'SSH连接失败', 'tabId': tab_id})

    except Exception as e:
        emit('terminal_error', {'message': f'连接错误: {str(e)}', 'tabId': data.get('tabId')})

@socketio.on('terminal_input')
def handle_terminal_input(data):
    """处理终端输入"""
    tab_id = data.get('tabId')
    if request.sid in ssh_connections and tab_id in ssh_connections[request.sid]:
        terminal = ssh_connections[request.sid][tab_id]
        terminal.send_input(data.get('data', ''))

@socketio.on('terminal_resize')
def handle_terminal_resize(data):
    """处理终端大小调整"""
    tab_id = data.get('tabId')
    if request.sid in ssh_connections and tab_id in ssh_connections[request.sid]:
        terminal = ssh_connections[request.sid][tab_id]
        cols = data.get('cols', 80)
        rows = data.get('rows', 24)
        terminal.resize(cols, rows)

@socketio.on('terminal_disconnect')
def handle_terminal_disconnect(data):
    """处理单个终端断开连接"""
    tab_id = data.get('tabId')
    if request.sid in ssh_connections and tab_id in ssh_connections[request.sid]:
        terminal = ssh_connections[request.sid][tab_id]
        terminal.close()
        del ssh_connections[request.sid][tab_id]



if __name__ == '__main__':
    # 创建必要的目录
    os.makedirs(PACKAGES_DIR, exist_ok=True)
    os.makedirs('configs', exist_ok=True)
    os.makedirs('logs', exist_ok=True)

    # 检查是否在Docker环境中运行
    is_docker = os.getenv('DOCKER_MODE', 'false').lower() == 'true'
    is_production = os.getenv('FLASK_ENV', 'development') == 'production'
    is_development = os.getenv('FLASK_ENV', 'development') == 'development'

    print("🚀 服务器安装管理系统启动中... (清理重新部署完成!)")

    if is_docker and is_development:
        print("🐳 Docker开发模式启动")
        print("📁 访问地址: http://localhost:5001")
        print("🔥 热重载已启用 - 修改代码会自动重启")
    elif is_docker:
        print("🐳 Docker生产模式启动")
        print("📁 访问地址: http://localhost:5001")
    else:
        print("📁 访问地址: http://localhost:5001")
        print("🔥 热重载已启用 - 修改代码会自动重启")
        print("✅ 热重载测试 - 代码修改后自动重启！")

    # 根据环境选择启动模式
    if is_production:
        # 生产模式 - 允许使用Werkzeug（简化部署）
        socketio.run(
            app,
            debug=False,                    # 关闭调试模式
            host='0.0.0.0',                # 允许外部访问
            port=5003,                     # 端口
            use_reloader=False,            # 关闭热重载
            allow_unsafe_werkzeug=True     # 允许在生产环境使用Werkzeug
        )
    else:
        # 开发模式 (包括Docker开发模式)
        socketio.run(
            app,
            debug=True,                    # 启用调试模式
            host='0.0.0.0',               # 允许外部访问
            port=5001,                    # 端口
            use_reloader=True,            # 启用热重载
            reloader_type='stat',         # 使用stat方式监听文件变化
            allow_unsafe_werkzeug=True    # 允许在Docker环境使用Werkzeug
        )
