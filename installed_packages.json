{"packages": [{"file": "template/autoUpdateDisable.py", "name": "autoUpdateDisable", "type": "python", "status": "installed", "lastChecked": "2025-07-12T10:42:21.029207"}, {"file": "template/docker.py", "name": "docker", "type": "python", "status": "installed", "lastChecked": "2025-07-12T10:42:21.057923"}, {"file": "fridge/ghraphicDriver.py", "name": "ghraphicDriver", "type": "python", "status": "installed", "lastChecked": "2025-07-12T10:42:21.234180"}, {"file": "template/nomachine.py", "name": "nomachine", "type": "python", "status": "installed", "lastChecked": "2025-07-12T10:42:23.016326"}, {"file": "template/chrome.py", "name": "chrome", "type": "python", "status": "installed", "lastChecked": "2025-07-12T10:42:23.285646"}, {"file": "template/imageLauncher.py", "name": "imageLauncher", "type": "python", "status": "installed", "lastChecked": "2025-07-12T10:42:23.593245"}, {"file": "template/nfsMount.py", "name": "nfsMount", "type": "python", "status": "installed", "lastChecked": "2025-07-12T10:42:23.650384"}, {"file": "template/firefox.py", "name": "firefox", "type": "python", "status": "installed", "lastChecked": "2025-07-12T10:42:23.912240"}, {"file": "template/rustdesk.py", "name": "rustdesk", "type": "python", "status": "installed", "lastChecked": "2025-07-12T10:42:24.080902"}, {"file": "template/x11_switch.py", "name": "x11_switch", "type": "python", "status": "installed", "lastChecked": "2025-07-12T10:42:25.309434"}, {"file": "template/change_sources.sh", "name": "change_sources", "type": "shell", "status": "installed", "lastChecked": "2025-07-12T10:42:25.334648"}, {"file": "template/teamviewer.py", "name": "teamviewer", "type": "python", "status": "installed", "lastChecked": "2025-07-12T10:42:25.428264"}, {"file": "template/zerotier.py", "name": "zerotier", "type": "python", "status": "installed", "lastChecked": "2025-07-12T10:42:25.552935"}, {"file": "template/vscode.py", "name": "vscode", "type": "python", "status": "installed", "lastChecked": "2025-07-12T10:42:25.688931"}, {"file": "template/base.py", "name": "base", "type": "python", "status": "installed", "lastChecked": "2025-07-12T10:42:26.707294"}, {"file": "template/bashConfig.py", "name": "bashConfig", "type": "python", "status": "installed", "lastChecked": "2025-07-12T10:42:26.755231"}], "total_packages": 24, "installed_count": 16, "timeout_count": 0, "uninstalled_count": 8, "last_check": "2025-07-12T10:42:26.758556", "check_duration_ms": 5966, "check_duration_seconds": 5.97, "max_workers": 8, "check_method": "multithreaded"}