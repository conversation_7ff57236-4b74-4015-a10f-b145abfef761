<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>服务器安装管理系统</title>
    <link rel="stylesheet" href="/static/style.css">
</head>

<body>
    <div class="container">

        <!-- SSH连接区域 -->
        <section class="connection-section">
            <div class="section-header">
                <h3>📡 SSH连接配置</h3>
                <div class="section-actions">
                    <div id="connection-status" class="connection-status-text" style="display: none;"></div>
                    <div id="upload-status-text" class="upload-status-text" style="display: none;"></div>
                </div>
            </div>
            <div class="connection-form">
                <div class="input-group">
                    <label>服务器IP:</label>
                    <input type="text" id="host" placeholder="*************" value="*************">
                </div>
                <div class="input-group">
                    <label>用户名:</label>
                    <input type="text" id="username" placeholder="root" value="mxhou">
                </div>
                <div class="input-group">
                    <label>密码:</label>
                    <input type="password" id="password" placeholder="密码" value="zyc">
                </div>
                <div class="button-group-horizontal">
                    <button id="connect-btn" onclick="testConnection()">🔗 连接</button>
                    <button id="terminal-btn" onclick="openTerminal()" disabled>💻 打开终端</button>
                    <button id="check-btn" title="单击：静默检查并更新列表，双击：在终端中检查">🔍 检查所有</button>
                </div>
            </div>
        </section>

        <!-- 包管理区域 -->
        <section class="packages-section" id="package-management-section" style="display: none;">
            <div class="section-header">
                <h3>📦 软件包管理</h3>
                <div class="section-actions">
                    <input type="text" id="search-input" placeholder="🔍 搜索软件包..." onkeyup="searchPackages()" />
                    <button onclick="clearSearch()">清空搜索框</button>
                    <button onclick="selectAllSearchResults()">全选结果</button>
                    <button onclick="uninstallSelected()" class="uninstall-selected-btn" disabled>🗑️ 卸载选中</button>
                </div>
            </div>

            <!-- 可用软件包 -->
            <div class="available-packages">
                <div class="packages-container">
                    <div class="packages-list" id="packages-list">
                        <div class="loading">正在加载包列表...</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 安装队列区域 -->
        <section class="queue-section" id="install-queue-section" style="display: none;">
            <div class="section-header">
                <div class="queue-title">
                    <h3>⚡ 安装队列</h3>
                    <span class="queue-count-text">已选择 <span id="queue-count">0</span> 个包</span>
                </div>
                <div class="section-actions">
                    <button onclick="selectAllUninstalled()">📦 全选未安装</button>
                    <button onclick="clearQueue()">🗑️ 清空</button>
                    <button onclick="saveConfig()">💾 保存配置</button>
                    <input type="file" id="config-file" accept=".json" onchange="loadConfig()" style="display: none;">
                    <button onclick="document.getElementById('config-file').click()">📁 导入配置</button>
                    <button id="install-btn" onclick="startInstall()" disabled>🚀 开始安装</button>
                </div>
            </div>
            <div class="queue-container">
                <div class="queue-list" id="queue-list">
                    <div class="empty-queue">暂无选择的包</div>
                </div>
            </div>
        </section>

        <!-- 执行结果区域已移除，安装过程将在终端中显示 -->
    </div>

    <!-- 状态提示 -->
    <div id="toast" class="toast"></div>

    <!-- 引入Socket.IO库用于接收终端完成通知 -->
    <script src="/static/libs/socket.io.min.js"></script>
    <script src="/static/script.js"></script>
</body>

</html>