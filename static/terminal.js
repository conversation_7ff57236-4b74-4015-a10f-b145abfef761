// Web终端JavaScript逻辑 - 多标签页版本
let socket;
let connectionParams = {};
let terminals = new Map(); // 存储所有终端实例
let currentTabId = null;
let tabCounter = 0;
let commandExecuted = false; // 标记是否已执行自动命令

// 终端实例类
class TerminalInstance {
    constructor(id, title) {
        this.id = id;
        this.title = title;
        this.terminal = null;
        this.fitAddon = null;
        this.isConnected = false;
        this.status = 'connecting';
        this.element = null;
        this.tabElement = null;
    }

    create() {
        // 创建终端实例
        this.terminal = new Terminal({
            cursorBlink: true,
            fontSize: 14,
            fontFamily: 'Courier New, monospace',
            theme: {
                background: '#000000',
                foreground: '#ffffff',
                cursor: '#ffffff',
                selection: '#ffffff40'
            },
            scrollback: 1000,
            tabStopWidth: 4
        });

        // 创建自适应插件
        this.fitAddon = new FitAddon.FitAddon();
        this.terminal.loadAddon(this.fitAddon);

        // 创建DOM元素
        this.element = document.createElement('div');
        this.element.className = 'terminal-wrapper';
        this.element.id = `terminal-${this.id}`;

        const terminalDiv = document.createElement('div');
        terminalDiv.className = 'terminal-instance';
        this.element.appendChild(terminalDiv);

        // 将终端附加到DOM
        this.terminal.open(terminalDiv);

        // 监听终端输入
        this.terminal.onData(data => {
            if (socket && this.isConnected) {
                socket.emit('terminal_input', {
                    data: data,
                    tabId: this.id
                });
            }
        });

        // 监听终端大小变化
        this.terminal.onResize(size => {
            if (socket && this.isConnected) {
                socket.emit('terminal_resize', {
                    cols: size.cols,
                    rows: size.rows,
                    tabId: this.id
                });
            }
        });

        // 显示欢迎信息
        this.terminal.writeln('\x1b[1;32m欢迎使用Web终端\x1b[0m');
        this.terminal.writeln('\x1b[1;36m正在连接到服务器...\x1b[0m');

        return this.element;
    }

    activate() {
        if (this.element) {
            this.element.classList.add('active');
            this.terminal.focus();
            // 调整终端大小
            setTimeout(() => {
                this.fitAddon.fit();
            }, 100);
        }
        if (this.tabElement) {
            this.tabElement.classList.add('active');
        }
    }

    deactivate() {
        if (this.element) {
            this.element.classList.remove('active');
        }
        if (this.tabElement) {
            this.tabElement.classList.remove('active');
        }
    }

    updateStatus(status) {
        this.status = status;
        if (this.tabElement) {
            const statusElement = this.tabElement.querySelector('.tab-status');
            statusElement.className = `tab-status ${status}`;
        }
    }

    write(data) {
        if (this.terminal) {
            this.terminal.write(data);
        }
    }

    writeln(data) {
        if (this.terminal) {
            this.terminal.writeln(data);
        }
    }

    clear() {
        if (this.terminal) {
            this.terminal.clear();
        }
    }

    resize() {
        if (this.fitAddon) {
            this.fitAddon.fit();
        }
    }

    destroy() {
        if (this.terminal) {
            this.terminal.dispose();
        }
        if (this.element) {
            this.element.remove();
        }
        if (this.tabElement) {
            this.tabElement.remove();
        }
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function () {
    // 从URL参数获取连接信息
    const urlParams = new URLSearchParams(window.location.search);
    connectionParams = {
        host: urlParams.get('host'),
        username: urlParams.get('username'),
        password: urlParams.get('password') ? atob(urlParams.get('password')) : null,
        command: urlParams.get('command') ? decodeURIComponent(urlParams.get('command')) : null,
        operation: urlParams.get('operation') || 'unknown', // 操作类型：install/uninstall/check/check_all
        packages: urlParams.get('packages') ? JSON.parse(decodeURIComponent(urlParams.get('packages'))) : []
    };

    // 调试信息
    console.log('终端连接参数:', connectionParams);
    console.log('包信息:', connectionParams.packages);

    // 验证连接参数
    if (!connectionParams.host || !connectionParams.username || !connectionParams.password) {
        showError('连接参数不完整，请从主页面重新打开终端');
        return;
    }

    // 更新标题
    let titleSuffix = '';
    if (connectionParams.operation === 'check') {
        titleSuffix = ' - 包状态检查';
    } else if (connectionParams.operation === 'check_all') {
        titleSuffix = ' - 检查所有包状态';
    } else if (connectionParams.operation === 'install') {
        titleSuffix = ' - 软件安装';
    } else if (connectionParams.operation === 'uninstall') {
        titleSuffix = ' - 软件卸载';
    }
    updateTitle(`${connectionParams.username}@${connectionParams.host}${titleSuffix}`);

    // 初始化WebSocket连接
    initSocket();

    // 创建第一个终端标签页
    createNewTab();

    // 绑定窗口事件
    bindWindowEvents();
});

// 创建新的终端标签页
function createNewTab() {
    tabCounter++;
    const tabId = `tab-${tabCounter}`;
    const title = `终端 ${tabCounter}`;

    // 创建终端实例
    const terminalInstance = new TerminalInstance(tabId, title);
    terminals.set(tabId, terminalInstance);

    // 创建标签页元素
    createTabElement(terminalInstance);

    // 创建终端元素并添加到容器
    const terminalElement = terminalInstance.create();
    document.getElementById('terminals-container').appendChild(terminalElement);

    // 切换到新标签页
    switchToTab(tabId);

    // 连接终端
    connectTerminal(tabId);

    return tabId;
}

// 创建标签页元素
function createTabElement(terminalInstance) {
    const tabsNav = document.getElementById('tabs-nav');

    const tab = document.createElement('div');
    tab.className = 'tab';
    tab.id = `tab-${terminalInstance.id}`;
    tab.onclick = () => switchToTab(terminalInstance.id);

    tab.innerHTML = `
        <div class="tab-status connecting"></div>
        <div class="tab-title">${terminalInstance.title}</div>
        <button class="tab-close" onclick="closeTab('${terminalInstance.id}', event)">&times;</button>
    `;

    tabsNav.appendChild(tab);
    terminalInstance.tabElement = tab;
}

// 切换到指定标签页
function switchToTab(tabId) {
    // 隐藏所有终端
    terminals.forEach((terminal, id) => {
        terminal.deactivate();
    });

    // 显示指定终端
    const terminal = terminals.get(tabId);
    if (terminal) {
        terminal.activate();
        currentTabId = tabId;
        updateConnectionStatus(terminal.status, getStatusText(terminal.status));
    }
}

// 关闭标签页
function closeTab(tabId, event) {
    if (event) {
        event.stopPropagation();
    }

    const terminal = terminals.get(tabId);
    if (terminal) {
        // 断开连接
        if (socket) {
            socket.emit('terminal_disconnect', { tabId: tabId });
        }

        // 销毁终端
        terminal.destroy();
        terminals.delete(tabId);

        // 如果关闭的是当前标签页，切换到其他标签页
        if (currentTabId === tabId) {
            const remainingTabs = Array.from(terminals.keys());
            if (remainingTabs.length > 0) {
                switchToTab(remainingTabs[0]);
            } else {
                currentTabId = null;
                updateConnectionStatus('disconnected', '无活动连接');
            }
        }
    }
}

// 连接终端
function connectTerminal(tabId) {
    if (socket) {
        socket.emit('terminal_connect', {
            ...connectionParams,
            tabId: tabId
        });
    }
}

// 初始化WebSocket连接
function initSocket() {
    // 创建Socket.IO连接
    socket = io();

    // 连接成功
    socket.on('connect', () => {
        console.log('WebSocket连接成功');
    });

    // 连接断开
    socket.on('disconnect', () => {
        console.log('WebSocket连接断开');
        // 更新所有终端状态
        terminals.forEach(terminal => {
            terminal.isConnected = false;
            terminal.updateStatus('disconnected');
            terminal.writeln('\r\n\x1b[1;31m连接已断开\x1b[0m');
        });
        updateConnectionStatus('disconnected', '连接断开');
    });

    // 终端连接成功
    socket.on('terminal_connected', (data) => {
        console.log('终端连接成功:', data);
        const terminal = terminals.get(data.tabId);
        if (terminal) {
            terminal.isConnected = true;
            terminal.updateStatus('connected');
            terminal.clear();
            terminal.terminal.focus();

            // 如果是当前活动标签页，更新状态显示
            if (currentTabId === data.tabId) {
                updateConnectionStatus('connected', '已连接');
            }

            // 如果有预设命令，自动执行
            if (connectionParams.command && data.tabId === currentTabId && !commandExecuted) {
                setTimeout(() => {
                    // 显示即将执行的命令
                    terminal.terminal.write(`\r\n\x1b[32m[自动执行] ${connectionParams.command}\x1b[0m\r\n`);

                    // 执行命令
                    socket.emit('terminal_input', {
                        data: connectionParams.command + '\n',
                        tabId: data.tabId
                    });

                    // 标记命令已执行
                    commandExecuted = true;
                }, 1000); // 延迟1秒执行，确保终端完全准备好
            }
        }
        hideLoading();
    });

    // 终端输出
    socket.on('terminal_output', (data) => {
        const terminal = terminals.get(data.tabId);
        if (terminal) {
            terminal.write(data.data);

            // 检测检查命令完成
            if (connectionParams.operation === 'check_all') {
                checkForCommandCompletion(data.data, data.tabId);
            }
        }
    });

    // 终端错误
    socket.on('terminal_error', (data) => {
        console.error('终端错误:', data);
        const terminal = terminals.get(data.tabId);
        if (terminal) {
            terminal.isConnected = false;
            terminal.updateStatus('error');

            // 如果是当前活动标签页，更新状态显示
            if (currentTabId === data.tabId) {
                updateConnectionStatus('error', '连接错误');
            }
        }
        hideLoading();
        showError(data.message);
    });

    // 连接错误
    socket.on('connect_error', (error) => {
        console.error('WebSocket连接错误:', error);
        updateConnectionStatus('error', '连接失败');
        hideLoading();
        showError('WebSocket连接失败: ' + error.message);
    });
}

// 检测命令完成
function checkForCommandCompletion(output, tabId) {
    // 检测检查命令是否完成
    if (output.includes('已保存检查结果到 installed_packages.json')) {
        console.log('检测到检查命令完成，准备刷新包列表');

        // 延迟1秒后刷新包列表，确保文件已完全写入
        setTimeout(() => {
            refreshPackageListFromParent();
        }, 1000);
    }
}

// 通知父窗口刷新包列表
function refreshPackageListFromParent() {
    try {
        // 检查是否有父窗口（opener）
        if (window.opener && !window.opener.closed) {
            // 调用父窗口的刷新函数
            if (typeof window.opener.refreshPackageListAfterCheck === 'function') {
                window.opener.refreshPackageListAfterCheck();
                console.log('已通知父窗口刷新包列表');
            } else {
                console.log('父窗口没有 refreshPackageListAfterCheck 函数');
            }
        } else {
            console.log('没有找到父窗口或父窗口已关闭');
        }
    } catch (error) {
        console.error('通知父窗口刷新包列表失败:', error);
    }
}

// 绑定窗口事件
function bindWindowEvents() {
    // 窗口大小变化时调整终端大小
    window.addEventListener('resize', () => {
        terminals.forEach(terminal => {
            if (terminal.fitAddon) {
                setTimeout(() => {
                    terminal.resize();
                }, 100);
            }
        });
    });

    // 阻止默认的键盘快捷键
    document.addEventListener('keydown', (e) => {
        // 阻止Ctrl+A等快捷键
        if (e.ctrlKey && ['a', 's', 'f'].includes(e.key.toLowerCase())) {
            e.preventDefault();
        }

        // Ctrl+T 新建标签页
        if (e.ctrlKey && e.key.toLowerCase() === 't') {
            e.preventDefault();
            createNewTab();
        }

        // Ctrl+W 关闭当前标签页
        if (e.ctrlKey && e.key.toLowerCase() === 'w') {
            e.preventDefault();
            if (currentTabId && terminals.size > 1) {
                closeTab(currentTabId);
            }
        }
    });
}

// 获取状态文本
function getStatusText(status) {
    switch (status) {
        case 'connected': return '已连接';
        case 'connecting': return '连接中...';
        case 'disconnected': return '已断开';
        case 'error': return '连接错误';
        default: return '未知状态';
    }
}

// 更新连接状态
function updateConnectionStatus(status, text) {
    const indicator = document.getElementById('status-indicator');
    const statusText = document.getElementById('status-text');

    indicator.className = `status-indicator ${status}`;
    statusText.textContent = text;

    // 更新重连按钮状态
    const reconnectBtn = document.getElementById('reconnect-btn');
    reconnectBtn.disabled = status === 'connected';
}

// 更新标题
function updateTitle(title) {
    document.getElementById('terminal-title').textContent = title;
    document.title = `${title} - Web终端`;
}

// 隐藏加载界面
function hideLoading() {
    const loading = document.getElementById('loading');
    loading.style.display = 'none';
}

// 显示错误
function showError(message) {
    const errorModal = document.getElementById('error-modal');
    const errorMessage = document.getElementById('error-message');

    errorMessage.textContent = message;
    errorModal.style.display = 'flex';
}

// 关闭错误模态框
function closeErrorModal() {
    const errorModal = document.getElementById('error-modal');
    errorModal.style.display = 'none';
}

// 重新连接当前标签页
function reconnectCurrentTab() {
    if (!currentTabId) return;

    const terminal = terminals.get(currentTabId);
    if (!terminal) return;

    // 重置状态
    terminal.isConnected = false;
    terminal.updateStatus('connecting');
    updateConnectionStatus('connecting', '重新连接中...');

    // 显示加载界面
    document.getElementById('loading').style.display = 'flex';

    // 清空终端
    terminal.clear();
    terminal.writeln('\x1b[1;36m正在重新连接...\x1b[0m');

    // 重新连接
    setTimeout(() => {
        connectTerminal(currentTabId);
    }, 1000);
}

// 清屏当前终端
function clearCurrentTerminal() {
    if (currentTabId) {
        const terminal = terminals.get(currentTabId);
        if (terminal) {
            terminal.clear();
        }
    }
}

// 切换全屏
function toggleFullscreen() {
    const container = document.querySelector('.terminal-container');

    if (!document.fullscreenElement) {
        container.requestFullscreen().then(() => {
            container.classList.add('fullscreen');
            // 全屏后调整终端大小
            setTimeout(() => {
                if (fitAddon) {
                    fitAddon.fit();
                }
            }, 100);
        }).catch(err => {
            console.error('无法进入全屏模式:', err);
        });
    } else {
        document.exitFullscreen().then(() => {
            container.classList.remove('fullscreen');
            // 退出全屏后调整终端大小
            setTimeout(() => {
                if (fitAddon) {
                    fitAddon.fit();
                }
            }, 100);
        });
    }
}

// 监听全屏变化事件
document.addEventListener('fullscreenchange', () => {
    const container = document.querySelector('.terminal-container');
    if (!document.fullscreenElement) {
        container.classList.remove('fullscreen');
    }
});


